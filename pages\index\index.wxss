.container {
  background-color: #f1f1f1;
  min-height: 100vh;
}

.order-tabs {
  display: flex;
  background-color: #fff;
  height: 120rpx;
  align-items: center;
  border-radius: 0 0 32rpx 32rpx;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  text-align: center;
  color: #666;
  font-size: 28rpx;
  position: relative;
  line-height: 70rpx;
}

.tab-item.active {
  color: #333;
  font-weight: bold;
  font-size: 30rpx;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 8rpx;
  border-radius: 8rpx;
  background-color: #FF4391;
}

.order-list {
  height: calc(100vh - 520rpx);
  margin: 30rpx 0;
}

.order-item {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 10rpx;
  padding: 20rpx;
  position: relative;
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 10rpx;
}

.order-number {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 28rpx;
  color: #FF4391;
}

.order-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  border-radius: 150rpx;
}

.product-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-service {
  background: rgba(47, 131, 255, 0.1);
  border-radius: 8px;
  padding: 8rpx 12rpx;
  width: 100%;
  line-height: 50rpx;
  margin-top: 16rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

/* 价格容器 */
.price-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}

/* 原价样式 */
.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

/* 实付金额样式 */
.total-fee {
  font-size: 32rpx;
  color: #ff4391;
  font-weight: bold;
}

.magin-bottom {
  margin-bottom: 20rpx;
  align-items: baseline;
}

.magin-bottom text:nth-child(1) {
  color: rgba(255, 67, 145, 1);
  margin-right: 20rpx;
  margin-top: 2rpx;
}

.action-btn {
  padding: 10rpx 20rpx;
  border-radius: 5rpx;
  font-size: 24rpx;
  border-radius: 40rpx;
  margin-left: 20rpx;
  background: rgba(47, 131, 255, 1);
  color: white;
  border-radius: 40rpx;
  text-align: center;
  line-height: 40rpx;
}

.empty-list {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 200rpx;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
}

.empty-text {
  margin-top: 20rpx;
  color: #999;
}

/* 地址文本样式 */
.address-text {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 用户备注样式 */
.user-remark {
  color: #666;
  font-size: 26rpx;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}