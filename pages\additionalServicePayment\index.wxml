<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-icon">
      <image src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNGRkY5RkYiLz4KPHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxNiIgeT0iMTYiPgo8cGF0aCBkPSJNMTYgMjhDMjIuNjI3NCAyOCAyOCAyMi42Mjc0IDI4IDE2QzI4IDkuMzcyNTggMjIuNjI3NCA0IDE2IDRDOS4zNzI1OCA0IDQgOS4zNzI1OCA0IDE2QzQgMjIuNjI3NCA5LjM3MjU4IDI4IDE2IDI4WiIgZmlsbD0iIzRDQUY1MCIvPgo8cGF0aCBkPSJNMTMuMzMzMyAxNkwxNS4zMzMzIDE4TDE4LjY2NjcgMTQuNjY2NyIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cjwvc3ZnPgo=" class="payment-icon"></image>
    </view>
    <view class="header-title">支付追加服务</view>
    <view class="header-subtitle">请确认以下追加服务信息</view>
  </view>

  <!-- 服务信息卡片 -->
  <view class="service-card">
    <view class="service-header">
      <view class="service-name">{{additionalService.serviceName}}</view>
      <view class="service-status {{additionalService.status}}">{{additionalService.statusText}}</view>
    </view>
    
    <!-- 订单信息 -->
    <view class="order-info">
      <view class="info-row">
        <text class="info-label">订单号：</text>
        <text class="info-value">{{orderInfo.sn}}</text>
      </view>
      <view class="info-row">
        <text class="info-label">客户：</text>
        <text class="info-value">{{orderInfo.customerName}}</text>
        <text class="info-phone" wx:if="{{orderInfo.customerPhone}}">({{orderInfo.customerPhone}})</text>
      </view>
      <view class="info-row" wx:if="{{additionalService.confirmTime}}">
        <text class="info-label">确认时间：</text>
        <text class="info-value">{{additionalService.confirmTime}}</text>
      </view>
    </view>

    <!-- 服务描述 -->
    <view class="service-description" wx:if="{{additionalService.description}}">
      <view class="description-label">服务描述：</view>
      <view class="description-content">{{additionalService.description}}</view>
    </view>
  </view>

  <!-- 价格信息 -->
  <view class="price-card">
    <view class="price-title">价格明细</view>
    
    <view class="price-row" wx:if="{{additionalService.originalPrice && additionalService.originalPrice > 0}}">
      <text class="price-label">服务原价</text>
      <text class="original-price">¥{{additionalService.originalPrice}}</text>
    </view>
    
    <view class="price-row total-row">
      <text class="price-label">应付金额</text>
      <text class="total-price">¥{{additionalService.servicePrice}}</text>
    </view>
  </view>

  <!-- 支付方式 -->
  <view class="payment-method">
    <view class="method-title">支付方式</view>
    <view class="method-item selected">
      <image src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjMDlCQjA3Ii8+Cjwvc3ZnPgo=" class="method-icon"></image>
      <text class="method-name">微信支付</text>
      <view class="method-check">
        <image src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjgiIGZpbGw9IiM0Q0FGNTASCZ8L2NpcmNsZT4KPHBhdGggZD0iTTUuMzMzMzMgOEw3LjMzMzMzIDEwTDEwLjY2NjcgNi42NjY2NyIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjMzMzMzIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==" class="check-icon"></image>
      </view>
    </view>
  </view>

  <!-- 支付按钮 -->
  <view class="payment-actions">
    <button class="detail-btn" bindtap="viewDetails">查看详情</button>
    <button 
      class="pay-btn {{loading ? 'loading' : ''}}" 
      bindtap="handlePayment"
      disabled="{{loading}}"
    >
      <text wx:if="{{!loading}}">确认支付 ¥{{additionalService.servicePrice}}</text>
      <text wx:else>支付中...</text>
    </button>
  </view>

  <!-- 温馨提示 -->
  <view class="tips">
    <view class="tips-title">温馨提示</view>
    <view class="tips-content">
      <text>• 支付成功后，服务状态将更新为"已付款"</text>
      <text>• 如有疑问，请联系客服</text>
      <text>• 支付过程中请勿关闭页面</text>
      <text wx:if="{{debugMode}}" style="color: #ff4391;">• 当前为调试模式，将模拟支付流程</text>
    </view>
  </view>
</view>
