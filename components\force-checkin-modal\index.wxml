<view wx:if="{{show}}" class="force-checkin-modal" catch:tap="preventTap">
  <view class="modal-mask"></view>
  <view class="modal-content">
    <!-- 警告图标 -->
    <view class="warning-icon">
      <text class="icon">⚠️</text>
    </view>
    
    <!-- 标题 -->
    <view class="modal-title">
      <text>出车打卡提醒</text>
    </view>
    
    <!-- 内容 -->
    <view class="modal-body">
      <text class="main-text">您已超过一周未进行出车打卡</text>
      <text class="sub-text" wx:if="{{lastCheckinDays > 0}}">距离上次打卡已过去 {{lastCheckinDays}} 天</text>
      <text class="sub-text" wx:else>您还未进行过出车打卡</text>
      <text class="requirement-text">根据公司规定，员工需要每周至少进行一次出车打卡</text>
    </view>
    
    <!-- 按钮 -->
    <view class="modal-footer">
      <button class="checkin-btn" bind:tap="goToCheckin">
        立即打卡
      </button>
    </view>
    
    <!-- 提示文字 -->
    <view class="modal-tip">
      <text>此步骤不可跳过，完成打卡后可正常使用应用</text>
    </view>
  </view>
</view>
