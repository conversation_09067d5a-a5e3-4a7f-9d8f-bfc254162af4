.custom-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999999;
}

.modal-container {
  width: 80%;
  background: white;
  border-radius: 10px;
  text-align: center;
  overflow: hidden;
}

.modal-image {
  padding-top: 20rpx;
  margin-top: 20rpx;
  width: 60%;
  height: 200rpx;
  object-fit: cover;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  margin: 15px 0 10px;
  color: #333;
}

.modal-content {
  padding: 0 15px 15px;
  color: #666;
  font-size: 14px;
}

.modal-buttons {
  display: flex;
  width: 100%;
}

.modal-btn {
  flex: 1;
  padding: 10px 0;
  background-color: rgba(255, 67, 145, 1);
  color: white;
  text-align: center;
  font-size: 16px;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  border-right: 1px solid #eee;
}

.confirm-btn {
  background-color: rgba(255, 67, 145, 1);
  color: white;
}