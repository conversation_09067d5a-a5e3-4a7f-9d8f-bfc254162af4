import request, { analysisRes } from '../request';
import config from '../config';

const { employeePromotion } = config.apiUrls;

export default {
  /**
   * 查看推广的客户列表
   * @param {string} employeeId 员工ID
   * @param {object} params 查询参数
   * @param {number} [params.current] 当前页码
   * @param {number} [params.pageSize] 每页数量
   * @returns {Promise<any>} 返回推广客户列表
   */
  async getCustomers(employeeId, params = {}) {
    const res = await request.get(
      employeePromotion.customers.replace('{employeeId}', employeeId),
      params
    );
    return analysisRes(res);
  },

  /**
   * 查看推广统计信息
   * @param {string} employeeId 员工ID
   * @returns {Promise<any>} 返回推广统计数据
   */
  async getStatistics(employeeId) {
    const res = await request.get(
      employeePromotion.statistics.replace('{employeeId}', employeeId),
      {
        hideLoading: true,
      }
    );
    return analysisRes(res);
  }
};
