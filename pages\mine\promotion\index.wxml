<!-- pages/mine/promotion/index.wxml -->
<view class="container">
  <!-- 推广码卡片 -->
  <view class="promotion-card">
    <view class="card-header">
      <text class="card-title">我的推广码</text>
      <text class="card-subtitle">邀请好友注册，获得推广奖励</text>
    </view>

    <view class="promotion-code-section">
      <view class="code-display" wx:if="{{promotionCode}}" bindtap="copyPromotionCode">
        <text class="code-text">{{promotionCode}}</text>
      </view>
      <view class="code-display no-code" wx:else>
        <text class="code-text placeholder">暂无推广码</text>
      </view>
      <view class="no-code-tip" wx:if="{{!promotionCode}}">
        <text class="tip-text">请联系管理员获取推广码</text>
      </view>
    </view>
  </view>

  <!-- 推广统计 -->
  <view class="statistics-card">
    <view class="card-header">
      <text class="card-title">推广统计</text>
    </view>

    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{statistics.totalPromoted}}</text>
        <text class="stat-label">累计推广</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-number">{{statistics.monthlyPromoted}}</text>
        <text class="stat-label">本月推广</text>
      </view>
    </view>
  </view>

  <!-- 推广用户列表 -->
  <view class="customers-card">
    <view class="card-header">
      <text class="card-title">推广用户</text>
      <view class="view-all" bindtap="viewCustomers">
        <text class="view-all-text">查看全部</text>
        <text class="arrow-icon">→</text>
      </view>
    </view>

    <view class="customers-preview">
      <text class="preview-text">点击查看您推广的用户列表</text>
    </view>
  </view>

  <!-- 推广说明 -->
  <view class="info-card">
    <view class="card-header">
      <text class="card-title">推广说明</text>
    </view>

    <view class="info-content">
      <view class="info-item">
        <text class="info-number">1</text>
        <text class="info-text">分享您的推广码给好友</text>
      </view>
      <view class="info-item">
        <text class="info-number">2</text>
        <text class="info-text">好友使用推广码注册成功</text>
      </view>
      <view class="info-item">
        <text class="info-number">3</text>
        <text class="info-text">您将获得相应的推广奖励</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 模态框 -->
  <custom-modal show="{{showModal}}" bind:confirm="onModalConfirm" title="{{modalTitle}}" content="{{modalContent}}"></custom-modal>
</view>