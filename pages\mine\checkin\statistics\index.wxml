<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">打卡统计</text>
  </view>

  <view wx:if="{{loading}}" class="loading-state">
    <text>加载中...</text>
  </view>

  <view wx:else class="content">
    <!-- 标签切换 -->
    <view class="tab-bar">
      <view 
        class="tab-item {{selectedTab === 'overview' ? 'active' : ''}}"
        data-tab="overview"
        bind:tap="switchTab"
      >
        总览
      </view>
      <view 
        class="tab-item {{selectedTab === 'monthly' ? 'active' : ''}}"
        data-tab="monthly"
        bind:tap="switchTab"
      >
        月度
      </view>
      <view 
        class="tab-item {{selectedTab === 'weekly' ? 'active' : ''}}"
        data-tab="weekly"
        bind:tap="switchTab"
      >
        周度
      </view>
      <view 
        class="tab-item {{selectedTab === 'daily' ? 'active' : ''}}"
        data-tab="daily"
        bind:tap="switchTab"
      >
        每日
      </view>
    </view>

    <!-- 总览页面 -->
    <view wx:if="{{selectedTab === 'overview'}}" class="overview-content">
      <!-- 核心指标 -->
      <view class="stats-grid">
        <view 
          class="stats-card primary"
          data-type="today"
          bind:tap="viewDetails"
        >
          <text class="stats-number">{{statistics.todayCount}}</text>
          <text class="stats-label">今日打卡</text>
        </view>
        
        <view 
          class="stats-card secondary"
          data-type="week"
          bind:tap="viewDetails"
        >
          <text class="stats-number">{{statistics.thisWeekCount}}</text>
          <text class="stats-label">本周打卡</text>
        </view>
        
        <view 
          class="stats-card tertiary"
          data-type="month"
          bind:tap="viewDetails"
        >
          <text class="stats-number">{{statistics.thisMonthCount}}</text>
          <text class="stats-label">本月打卡</text>
        </view>
        
        <view class="stats-card quaternary">
          <text class="stats-number">{{formatNumber(statistics.totalCount)}}</text>
          <text class="stats-label">累计打卡</text>
        </view>
      </view>

      <!-- 平均值统计 -->
      <view class="average-section">
        <view class="section-title">平均统计</view>
        <view class="average-grid">
          <view class="average-item">
            <text class="average-number">{{statistics.averagePerDay}}</text>
            <text class="average-label">日均打卡</text>
          </view>
          <view class="average-item">
            <text class="average-number">{{statistics.averagePerWeek}}</text>
            <text class="average-label">周均打卡</text>
          </view>
          <view class="average-item">
            <text class="average-number">{{statistics.averagePerMonth}}</text>
            <text class="average-label">月均打卡</text>
          </view>
        </view>
      </view>

      <!-- 最近7天 -->
      <view wx:if="{{statistics.recentDays.length > 0}}" class="recent-section">
        <view class="section-title">最近7天</view>
        <view class="recent-grid">
          <view 
            wx:for="{{statistics.recentDays}}" 
            wx:key="date"
            class="recent-item"
            data-type="date"
            data-date="{{item.date}}"
            bind:tap="viewDetails"
          >
            <text class="recent-date">{{item.dayName}}</text>
            <text class="recent-count">{{item.count}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 月度统计 -->
    <view wx:if="{{selectedTab === 'monthly'}}" class="monthly-content">
      <view class="section-title">月度打卡统计</view>
      
      <view wx:if="{{statistics.monthlyData.length === 0}}" class="empty-state">
        <text>暂无月度数据</text>
      </view>
      
      <view wx:else class="monthly-list">
        <view 
          wx:for="{{statistics.monthlyData}}" 
          wx:key="month"
          class="monthly-item"
        >
          <view class="monthly-info">
            <text class="monthly-name">{{getMonthName(item.month)}}</text>
            <text class="monthly-year">{{item.year}}年</text>
          </view>
          <view class="monthly-stats">
            <text class="monthly-count">{{item.count}}次</text>
            <text class="monthly-days">{{item.days}}天</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 周度统计 -->
    <view wx:if="{{selectedTab === 'weekly'}}" class="weekly-content">
      <view class="section-title">周度打卡统计</view>
      
      <view wx:if="{{statistics.weeklyData.length === 0}}" class="empty-state">
        <text>暂无周度数据</text>
      </view>
      
      <view wx:else class="weekly-list">
        <view 
          wx:for="{{statistics.weeklyData}}" 
          wx:key="week"
          class="weekly-item"
        >
          <view class="weekly-info">
            <text class="weekly-name">第{{item.week}}周</text>
            <text class="weekly-range">{{item.startDate}} - {{item.endDate}}</text>
          </view>
          <view class="weekly-stats">
            <text class="weekly-count">{{item.count}}次</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 每日统计 -->
    <view wx:if="{{selectedTab === 'daily'}}" class="daily-content">
      <view class="section-title">每日打卡分布</view>
      
      <view class="daily-chart">
        <view class="chart-header">
          <text>打卡次数分布</text>
        </view>
        
        <view class="chart-content">
          <view class="chart-item">
            <text class="chart-label">0次</text>
            <view class="chart-bar">
              <view class="chart-fill" style="width: 20%"></view>
            </view>
            <text class="chart-value">20%</text>
          </view>
          
          <view class="chart-item">
            <text class="chart-label">1次</text>
            <view class="chart-bar">
              <view class="chart-fill" style="width: 50%"></view>
            </view>
            <text class="chart-value">50%</text>
          </view>
          
          <view class="chart-item">
            <text class="chart-label">2次</text>
            <view class="chart-bar">
              <view class="chart-fill" style="width: 25%"></view>
            </view>
            <text class="chart-value">25%</text>
          </view>
          
          <view class="chart-item">
            <text class="chart-label">3次+</text>
            <view class="chart-bar">
              <view class="chart-fill" style="width: 5%"></view>
            </view>
            <text class="chart-value">5%</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
