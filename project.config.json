{"compileType": "miniprogram", "libVersion": "3.7.10", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "ignoreDevUnusedFiles": false, "ignoreUploadUnusedFiles": false, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "appid": "wxb59303831dd1ccac"}