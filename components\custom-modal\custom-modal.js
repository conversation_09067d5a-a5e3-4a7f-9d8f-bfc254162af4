Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: '敬请期待'
    },
    content: {
      type: String,
      value: '该功能正在开发中，我们正努力为您带来更好的体验！'
    },
    confirmText: {
      type: String,
      value: '知道了'
    },
    showCancel: {
      type: Boolean,
      value: false
    },
    cancelText: {
      type: String,
      value: '取消'
    },
    imageUrl: {
      type: String,
      value: '//xian7.zos.ctyun.cn/pet/static/login.png'
    }
  },
  methods: {
    onConfirm() {
      this.triggerEvent('confirm')
      this.setData({
        show: false
      })
    },
    onCancel() {
      this.triggerEvent('cancel')
      this.setData({
        show: false
      })
    }
  }
})