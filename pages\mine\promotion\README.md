# 员工端推广码功能

## 功能概述

实现了员工端的推广码相关功能，包括：
1. 展示员工的推广码
2. 查看推广统计信息
3. 查看推广用户列表
4. 推广码复制和分享功能

## 文件结构

```
pages/mine/promotion/
├── index.js              # 推广码主页面逻辑
├── index.json            # 推广码主页面配置
├── index.wxml            # 推广码主页面模板
├── index.wxss            # 推广码主页面样式
├── customers/
│   ├── index.js          # 推广用户列表页面逻辑
│   ├── index.json        # 推广用户列表页面配置
│   ├── index.wxml        # 推广用户列表页面模板
│   └── index.wxss        # 推广用户列表页面样式
└── README.md             # 功能说明文档
```

## API接口

### 1. 查看推广统计信息
- **接口**: `GET /employee-promotion/employee/{employeeId}/statistics`
- **功能**: 获取员工的推广统计数据（累计推广、本月推广）

### 2. 查看推广用户列表
- **接口**: `GET /employee-promotion/employee/{employeeId}/customers`
- **功能**: 获取员工推广的用户列表，支持分页

## 主要功能

### 推广码主页面 (`/pages/mine/promotion/index`)
- 显示员工的推广码
- 提供复制推广码功能
- 提供分享推广码功能
- 显示推广统计信息（累计推广、本月推广）
- 提供查看推广用户列表的入口
- 显示推广说明

### 推广用户列表页面 (`/pages/mine/promotion/customers/index`)
- 显示推广用户列表
- 支持下拉刷新和上拉加载更多
- 显示用户基本信息（头像、昵称、手机号、会员状态、积分等）
- 显示用户注册时间
- 空状态处理

## 使用方式

1. 在"我的"页面点击"我的推广"入口
2. 进入推广码主页面，可以：
   - 查看和复制推广码
   - 查看推广统计
   - 点击"查看全部"进入推广用户列表
3. 在推广用户列表页面可以查看所有推广的用户信息

## 技术特点

1. **响应式设计**: 适配不同屏幕尺寸
2. **用户体验**: 提供加载状态、空状态、错误处理
3. **性能优化**: 使用WXS处理页面逻辑，提高渲染性能
4. **代码复用**: 遵循现有项目的代码规范和组件使用方式
5. **数据管理**: 支持分页加载，避免一次性加载大量数据

## 注意事项

1. 推广码从员工信息的`promotionCode`字段获取，通过`GET /employees/{id}`接口获取最新数据
2. 如果员工没有推广码，会显示"暂无推广码"并提示联系管理员
3. 分享功能目前只是显示模态框，可以根据需要集成微信分享API
4. 所有API调用都包含了错误处理和用户提示
5. 页面支持下拉刷新功能，保证数据的实时性
6. 页面会自动同步最新的员工信息到本地存储
