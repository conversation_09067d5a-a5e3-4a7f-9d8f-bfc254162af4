// components/force-checkin-modal/index.js
Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    lastCheckinDays: {
      type: Number,
      value: 0
    }
  },

  data: {
    // 组件内部数据
    titleTapCount: 0,
    titleTapTimer: null
  },

  methods: {
    // 前往打卡
    goToCheckin() {
      this.triggerEvent('gotoCheckin');
    },

    // 阻止事件冒泡
    preventTap() {
      // 空方法，用于阻止点击事件冒泡
    },

    // 标题点击事件（调试用，连续点击5次可关闭弹窗）
    onTitleTap() {
      this.data.titleTapCount++;

      if (this.data.titleTapTimer) {
        clearTimeout(this.data.titleTapTimer);
      }

      this.data.titleTapTimer = setTimeout(() => {
        this.data.titleTapCount = 0;
      }, 2000); // 2秒内重置计数

      if (this.data.titleTapCount >= 5) {
        console.log('调试模式：强制关闭打卡弹窗');
        this.triggerEvent('forceClose');
        this.data.titleTapCount = 0;
      }
    }
  }
});
