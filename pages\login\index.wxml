<!--pages/login/index.wxml-->
<view class='login-page'>
  <view class='login-wrapper'>
    <view class="logo-image-wrapper">
      <image src="//xian7.zos.ctyun.cn/pet/static/logo.png" mode="widthFix"></image>
    </view>
     <text class="logo-text">
      您好！
      欢迎来到贝宠乐福工作台
     </text>
    <view class="login-image-wrapper">
      <image src="//xian7.zos.ctyun.cn/pet/static/login.png" mode="widthFix"></image>
    </view>
  </view>
  <view class="btn-container">
    <!-- 未同意协议时显示的按钮 -->
    <button wx:if="{{globalData.agree !== '1'}}" class="get-phone-btn" bindtap="handleAgreeFirst">授权手机号登录</button>

    <!-- 已同意协议时显示的按钮 -->
    <button wx:if="{{globalData.agree === '1'}}" class="get-phone-btn" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">授权手机号登录</button>
    <view class="tip">
      <view class="agreed" catchtap="navigateTo" data-type="agreeFunction">
        <text wx:if="{{globalData.agree=='1'}}" class="flex icon3 diygw-col-0 icon3-clz diy-icon-roundcheck"></text>
        <text wx:if="{{globalData.agree!='1'}}" class="flex icon2 diygw-col-0 icon2-clz diy-icon-round"></text>
        <text class="diygw-col-0"> 我已阅读并同意</text>
      </view>
      <text catchtap="navigateTo" data-type="page" data-url="/pages/null" data-newstype="privacy" class="text-link"> 《贝宠乐福用户协议》</text>
      <text catchtap="navigateTo" data-type="page" data-url="/pages/null" data-newstype="user" class="text-link"> 《贝宠乐福隐私协议》</text>
      <text> 授权预留手机号，登录成功后将自动匹配贝宠乐福账号</text>
    </view>
  </view>
</view>