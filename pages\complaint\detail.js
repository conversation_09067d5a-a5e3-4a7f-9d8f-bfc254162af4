// pages/complaint/detail.js
import employeeSuggestionApi from '../../api/modules/employeeSuggestion';
import Session from '../../common/Session';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    complaintDetail: null,
    loading: true,
    
    // 状态映射
    statusMap: {
      pending: { text: '待处理', color: '#ff9500', desc: '您的建议已提交，我们会尽快处理' },
      processing: { text: '处理中', color: '#007aff', desc: '我们正在处理您的建议，请耐心等待' },
      resolved: { text: '已解决', color: '#34c759', desc: '您的建议已处理完成' },
      closed: { text: '已关闭', color: '#8e8e93', desc: '建议已关闭' }
    },

    // 分类映射
    categoryMap: {
      suggestion: '建议'
    },

    subCategoryMap: {
      platform: '平台建议',
      service: '服务建议',
      workflow: '流程建议'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const userInfo = Session.getUser();
    this.setData({ userInfo });
    
    if (options.id) {
      this.loadSuggestionDetail(options.id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载建议详情
   */
  async loadSuggestionDetail(suggestionId) {
    try {
      const { userInfo } = this.data;
      if (!userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      const detail = await employeeSuggestionApi.detail(userInfo.id, suggestionId);
      if (detail) {
        this.setData({ complaintDetail: detail });
      } else {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载建议详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    const { complaintDetail } = this.data;
    wx.previewImage({
      current: url,
      urls: complaintDetail.photoURLs || []
    });
  },

  /**
   * 编辑建议
   */
  editSuggestion() {
    const { complaintDetail } = this.data;

    if (complaintDetail.status !== 'pending') {
      wx.showToast({
        title: '只有待处理状态的建议才能编辑',
        icon: 'none'
      });
      return;
    }

    // 跳转到创建页面，传递编辑参数
    wx.navigateTo({
      url: `/pages/complaint/index?mode=edit&id=${complaintDetail.id}`
    });
  },

  /**
   * 删除建议
   */
  deleteSuggestion() {
    const { complaintDetail } = this.data;

    if (complaintDetail.status !== 'pending') {
      wx.showToast({
        title: '只有待处理状态的建议才能删除',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这条建议吗？',
      success: async (res) => {
        if (res.confirm) {
          await this.performDelete();
        }
      }
    });
  },

  /**
   * 执行删除
   */
  async performDelete() {
    try {
      wx.showLoading({ title: '删除中...' });

      const { userInfo, complaintDetail } = this.data;
      await employeeSuggestionApi.delete(userInfo.id, complaintDetail.id);

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      console.error('删除建议失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 联系客服
   */
  contactService() {
    // 这里可以跳转到客服页面或拨打客服电话
    wx.showModal({
      title: '联系客服',
      content: '如需进一步沟通，请联系客服：18591969898',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '18591969898'
          });
        }
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
});
