// components/custom-navbar/custom-navbar.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
		tabsDatas: [
			{ text: `约洗护`, icon: `` },
			{ text: `约美容`, icon: `` },
			// { text: `约喂养`, icon: `` },
		],
		tabsIndex: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

	changeTabs(evt) {
		let { index } = evt.currentTarget.dataset;
		if (index == this.data.tabsIndex) return;
		this.setData({
			tabsIndex: index
		});
	},
})