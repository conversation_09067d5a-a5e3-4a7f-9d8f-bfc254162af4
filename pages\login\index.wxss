/* pages/login/index.wxss */
.login-page {
  background-color: rgba(246, 246, 246, 1);
  height: 100vh;
  position:relative;
}
.login-wrapper{
  padding: 40rpx 40rpx 0rpx;
  background: rgba(47, 131, 255, 0.1);
  overflow: hidden;
  border-radius: 0 0 50vw 50vw;
}
.logo-text{
  text-align: center;
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  line-height: 70rpx;
}
.logo-image-wrapper,.login-image-wrapper{
  text-align: center;
}
.logo-image-wrapper image{
  width: 50vw;
}
.login-image-wrapper image{
  width: 60vw;
  margin-top: 80rpx;
}
.btn-container{
  margin: 100rpx auto 0;
  width: 80vw;
}
.btn-container button{
  background: rgba(255, 67, 145, 1);
  box-shadow: 0px 3px 0px 0px rgba(255, 67, 145, 0.3);
  color: #fff;
  border-radius: 40rpx;
  margin-bottom: 40rpx;
}
.btn-container > .tip{
  line-height: 50rpx;
  font-size: 22rpx;
}
.btn-container > .tip .icon3{
  margin-right: 10rpx;
  color: rgba(47, 131, 255, 1);
}
.btn-container > .tip .text-link{
  color: rgba(47, 131, 255, 1);
}
.btn-no-login{
  position: absolute;
  bottom: 60rpx;
  width: 200rpx;
  left: calc(50vw - 100rpx);
  border: none;
  background: none;
}