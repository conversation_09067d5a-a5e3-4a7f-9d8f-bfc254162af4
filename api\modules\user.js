import request, { analysisRes } from "../request";
import config from "../config";

const { user } = config.apiUrls;

export default {
  // 授权获取用户手机号
  async getPhoneNumber(code) {
    const res = await request.get(user.getPhoneNumber, {
      code,
      isMember: true, // 是员工端
      loadingText: "登录中...",
    });
    const data = analysisRes(res);
    return data;
  },

  // 用户登录
  async login(phone) {
    const res = await request.post(user.login, { phone });
    const data = analysisRes(res);
    return data;
  },

  // 更新用户信息
  updateProfile(data) {
    return request.post(user.updateProfile, data);
  },

  // 上传头像
  uploadAvatar(filePath) {
    return request.upload(user.uploadAvatar, filePath);
  },

  // 获取员工详细信息（包括评价）
  async getEmployeeInfo(employeeId) {
    const res = await request.get(user.getEmployeeInfo.replace('{id}', employeeId), {
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },
};
