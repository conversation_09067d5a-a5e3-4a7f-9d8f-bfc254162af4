.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.navbar-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 内容区域 */
.content {
  padding: 20rpx;
  padding-top: 120rpx; /* 为固定导航栏留出空间 */
}

/* 订单信息卡片 */
.order-info-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.service-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.service-extra {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.order-details {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 30rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

/* 服务评分卡片 */
.rating-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rating-label {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.rating-score {
  display: flex;
  align-items: center;
}

.stars {
  display: flex;
  margin-right: 15rpx;
}

.star {
  font-size: 36rpx;
  margin-right: 8rpx;
}

.star-full {
  color: #ffa500;
}

.star-half {
  color: #ffa500;
}

.star-empty {
  color: #e5e5e5;
}

.score-text {
  font-size: 32rpx;
  color: #ffa500;
  font-weight: 600;
}

/* 评价内容卡片 */
.comment-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.comment-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
}

/* 评价图片卡片 */
.images-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.review-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}



/* 评价时间 */
.time-info {
  text-align: center;
  margin-bottom: 20rpx;
}

.time-text {
  font-size: 26rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 用户备注样式 */
.user-remark {
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
  color: #333;
  font-size: 28rpx;
}
