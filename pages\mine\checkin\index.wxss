.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 页面标题 */
.header {
  background-color: #fff;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
}

.history-btn {
  background: linear-gradient(135deg, #ff4f8f 0%, #ff7ba7 100%);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  font-weight: 500;
}

/* 统计区域 */
.stats-section {
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  display: flex;
  justify-content: space-around;
  color: white;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.stats-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 15rpx rgba(102, 126, 234, 0.3);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  opacity: 0.9;
}

.stats-tip {
  position: absolute;
  bottom: 15rpx;
  right: 20rpx;
  display: flex;
  align-items: center;
  gap: 5rpx;
  opacity: 0.8;
}

.tip-text {
  font-size: 20rpx;
}

.tip-arrow {
  font-size: 18rpx;
  transition: transform 0.2s ease;
}

.stats-card:active .tip-arrow {
  transform: translateX(3rpx);
}

/* 区域标题 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.title-desc {
  font-size: 24rpx;
  color: #999;
}

/* 拍照区域 */
.photo-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

/* 照片类型切换标签 */
.photo-type-tabs {
  display: flex;
  margin: 20rpx 0;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
}

.photo-type-tabs .tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.photo-type-tabs .tab-item.active {
  background-color: #667eea;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.photo-type-tabs .tab-item .tab-text {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.photo-type-tabs .tab-item .tab-count {
  display: block;
  font-size: 22rpx;
  opacity: 0.8;
}

.photo-type-tabs .tab-item.active .tab-count {
  opacity: 1;
}

/* 照片类型切换标签 */
.photo-type-tabs {
  display: flex;
  margin: 20rpx 0;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
}

.photo-type-tabs .tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.photo-type-tabs .tab-item.active {
  background-color: #667eea;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.photo-type-tabs .tab-item .tab-text {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.photo-type-tabs .tab-item .tab-count {
  display: block;
  font-size: 22rpx;
  opacity: 0.8;
}

.photo-type-tabs .tab-item.active .tab-count {
  opacity: 1;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.photo-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
}

.photo-delete {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.photo-add {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.add-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.add-text {
  font-size: 24rpx;
}

.uploading-tip {
  text-align: center;
  color: #666;
  font-size: 24rpx;
  margin-top: 20rpx;
}

/* 描述区域 */
.description-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.description-input {
  width: 100%;
  min-height: 200rpx;
  border: 2rpx solid #eee;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

/* 位置区域 */
.location-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.location-empty {
  text-align: center;
}

.location-btn {
  background: #f0f0f0;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  color: #666;
  font-size: 28rpx;
}

.location-icon {
  font-size: 32rpx;
}

.location-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 10rpx;
}

.location-text {
  flex: 1;
}

.location-address {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
  font-weight: 500;
}

.location-coords {
  font-size: 22rpx;
  color: #999;
}

.location-clear {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

/* 提交按钮 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 600;
  background: #f0f0f0;
  color: #999;
  border: none;
  transition: all 0.3s ease;
}

.submit-btn.active {
  background: linear-gradient(135deg, #ff4f8f 0%, #ff7ba7 100%);
  color: #fff;
  box-shadow: 0 6rpx 20rpx rgba(255, 79, 143, 0.3);
}

.submit-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 79, 143, 0.3);
}

.submit-btn[disabled] {
  background: #f0f0f0 !important;
  color: #ccc !important;
  box-shadow: none !important;
}

/* 今日记录 */
.today-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.checkin-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.checkin-item {
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 10rpx;
}

.checkin-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.checkin-desc {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

.checkin-location {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
}

/* 分组照片显示 */
.checkin-photos-groups {
  margin-top: 15rpx;
}

.photo-group {
  margin-bottom: 20rpx;
}

.photo-group:last-child {
  margin-bottom: 0;
}

.group-title {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.photo-list {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
}

/* 保留原有样式用于兼容 */
.checkin-photos {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
}

.checkin-photo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 6rpx;
}


