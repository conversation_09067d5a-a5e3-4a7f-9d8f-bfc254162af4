/* components/custom-picker/index.wxss */
.time-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.time-picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.time-picker-container {
  position: absolute;
  bottom: 120rpx; /* 增加底部距离，避免被主菜单挡住 */
  left: 0;
  width: 100%;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  z-index: 10000; /* 确保在主菜单之上 */
  max-height: 70vh; /* 限制最大高度为视口高度的70% */
}

.time-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.time-picker-header .title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.cancel-btn, .confirm-btn {
  font-size: 28rpx;
  padding: 10rpx;
}

.cancel-btn {
  color: #999;
}

.confirm-btn {
  color: rgba(255, 67, 145, 1);
}

.picker-content {
  padding: 30rpx;
}

.picker-label {
  font-size: 28rpx;
  color: #333;
  padding: 20rpx 0;
  text-align: center;
}

/* 增加picker的高度和样式 */
.picker-inner {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 80rpx;
  padding: 0 20rpx;
  background-color: #f8f8f8;
  margin-bottom: 10rpx;
  border-top-left-radius: 12rpx;
  border-top-right-radius: 12rpx;
}

.picker-column {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.time-picker-view {
  width: 100%;
  height: 400rpx;
  background-color: #f8f8f8;
  border-bottom-left-radius: 12rpx;
  border-bottom-right-radius: 12rpx;
}

/* 增加选择器的可点击区域 */
.picker-item {
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  padding: 0 10rpx;
}

/* 选中项的样式 */
.picker-view-indicator {
  height: 80rpx;
  background-color: rgba(255, 67, 145, 0.1);
}

/* 美化选择器 */
picker-view-column {
  text-align: center;
}

/* 添加选中效果 */
picker-view-column .picker-item:nth-child(even) {
  background-color: rgba(248, 248, 248, 0.5);
}

/* 添加时间说明 */
.time-description {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  padding: 10rpx 0;
  background-color: #f8f8f8;
}