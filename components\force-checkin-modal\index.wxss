/* components/force-checkin-modal/index.wxss */
.force-checkin-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
  position: relative;
  width: 600rpx;
  background-color: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  text-align: center;
}

.warning-icon {
  margin-bottom: 30rpx;
}

.warning-icon .icon {
  font-size: 80rpx;
}

.modal-title {
  margin-bottom: 30rpx;
}

.modal-title text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-body {
  margin-bottom: 40rpx;
  text-align: center;
}

.main-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.sub-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.requirement-text {
  display: block;
  font-size: 26rpx;
  color: #ff6b35;
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #fff5f0;
  border-radius: 12rpx;
  border-left: 6rpx solid #ff6b35;
}

.modal-footer {
  margin-bottom: 20rpx;
}

.checkin-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.3);
}

.checkin-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.modal-tip {
  text-align: center;
}

.modal-tip text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}
