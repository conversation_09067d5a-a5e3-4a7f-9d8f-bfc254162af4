import dictionaryApi from '../api/modules/dictionary';

/**
 * 职位代码映射
 */
const POSITION_CODES = {
  XI_HU_SHI: '洗护师',
  MEI_RONG_SHI: '美容师'
};

/**
 * 服务类型代码映射
 */
const SERVICE_TYPE_CODES = {
  XI_HU: '洗护',
  MEI_RONG: '美容'
};

/**
 * 根据职位代码获取职位名称
 * @param {string} positionCode 职位代码
 * @returns {Promise<string>} 职位名称
 */
async function getPositionName(positionCode) {
  if (!positionCode) return '';
  
  try {
    // 先尝试从本地映射获取
    if (POSITION_CODES[positionCode]) {
      return POSITION_CODES[positionCode];
    }
    
    // 从字典API获取
    const positionList = await dictionaryApi.list('员工职位');
    const position = positionList.find(item => item.code === positionCode);
    return position ? position.name : positionCode;
  } catch (error) {
    console.error('获取职位名称失败:', error);
    return POSITION_CODES[positionCode] || positionCode || '';
  }
}

/**
 * 根据服务类型代码获取服务类型名称
 * @param {string} serviceTypeCode 服务类型代码
 * @returns {Promise<string>} 服务类型名称
 */
async function getServiceTypeName(serviceTypeCode) {
  if (!serviceTypeCode) return '';
  
  try {
    // 先尝试从本地映射获取
    if (SERVICE_TYPE_CODES[serviceTypeCode]) {
      return SERVICE_TYPE_CODES[serviceTypeCode];
    }
    
    // 从字典API获取
    const serviceTypeList = await dictionaryApi.list('服务类型');
    const serviceType = serviceTypeList.find(item => item.code === serviceTypeCode);
    return serviceType ? serviceType.name : serviceTypeCode;
  } catch (error) {
    console.error('获取服务类型名称失败:', error);
    return SERVICE_TYPE_CODES[serviceTypeCode] || serviceTypeCode || '';
  }
}

/**
 * 检查职位限制错误信息
 * @param {string} errorMessage 错误信息
 * @returns {boolean} 是否为职位限制错误
 */
function isPositionLimitError(errorMessage) {
  if (!errorMessage) return false;
  
  const positionLimitKeywords = [
    '不能接此类订单',
    '职位限制',
    '洗护师不能接',
    '美容师不能接',
    '职位不匹配'
  ];
  
  return positionLimitKeywords.some(keyword => errorMessage.includes(keyword));
}

/**
 * 获取职位限制的友好提示信息
 * @param {string} position 员工职位代码
 * @param {string} serviceType 服务类型代码
 * @returns {Promise<string>} 友好提示信息
 */
async function getPositionLimitMessage(position, serviceType) {
  try {
    const positionName = await getPositionName(position);
    const serviceTypeName = await getServiceTypeName(serviceType);
    
    if (position === 'XI_HU_SHI' && serviceType === 'MEI_RONG') {
      return `${positionName}只能接洗护类订单，无法接${serviceTypeName}订单`;
    }
    
    return `您的职位（${positionName}）无法接此类订单（${serviceTypeName}）`;
  } catch (error) {
    console.error('获取职位限制提示信息失败:', error);
    return '您的职位无法接此类订单';
  }
}

/**
 * 检查员工是否可以接指定类型的订单
 * @param {string} position 员工职位代码
 * @param {string} serviceType 服务类型代码
 * @returns {boolean} 是否可以接单
 */
function canAcceptOrder(position, serviceType) {
  if (!position || !serviceType) return true; // 如果信息不完整，默认允许
  
  // 洗护师只能接洗护订单
  if (position === 'XI_HU_SHI') {
    return serviceType === 'XI_HU';
  }
  
  // 美容师可以接洗护和美容订单
  if (position === 'MEI_RONG_SHI') {
    return serviceType === 'XI_HU' || serviceType === 'MEI_RONG';
  }
  
  // 其他职位默认允许
  return true;
}

/**
 * 获取员工可接的服务类型列表
 * @param {string} position 员工职位代码
 * @returns {string[]} 可接的服务类型代码列表
 */
function getAllowedServiceTypes(position) {
  if (!position) return []; // 如果没有职位信息，返回空数组
  
  switch (position) {
    case 'XI_HU_SHI':
      return ['XI_HU']; // 洗护师只能接洗护订单
    case 'MEI_RONG_SHI':
      return ['XI_HU', 'MEI_RONG']; // 美容师可以接洗护和美容订单
    default:
      return []; // 未知职位，返回空数组
  }
}

export {
  getPositionName,
  getServiceTypeName,
  isPositionLimitError,
  getPositionLimitMessage,
  canAcceptOrder,
  getAllowedServiceTypes,
  POSITION_CODES,
  SERVICE_TYPE_CODES
};

export default {
  getPositionName,
  getServiceTypeName,
  isPositionLimitError,
  getPositionLimitMessage,
  canAcceptOrder,
  getAllowedServiceTypes,
  POSITION_CODES,
  SERVICE_TYPE_CODES
};
