.container {
  background-color: #f4f4f4;
  padding: 20rpx;
  font-family: Arial, sans-serif;
}

.order-header {
  text-align: center;
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.order-info {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.order-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
}

.product-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-service {
  background: rgba(47, 131, 255, 0.1);
  border-radius: 8px;
  padding: 8rpx 12rpx;
  width: 100%;
  line-height: 50rpx;
  margin-top: 16rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.product-quantity {
  font-size: 24rpx;
  color: #999;
}

.paid-money {
  text-align: right;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.magin-bottom {
  margin-bottom: 20rpx;
}

.order-details {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  min-height: 60rpx;
  padding: 8rpx 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  margin-right: 20rpx;
  line-height: 1.4;
}
/* 底部操作按钮容器 */
.bottom-action-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.action-buttons-row {
  display: flex;
  gap: 20rpx;
  justify-content: center;
  padding-bottom: 20rpx;
}

/* 现代化按钮样式 */
.modern-action-btn {
  flex: 1;
  display: flex;
  gap: 8rpx;
  align-items: center;
  justify-content: center;
  padding: 20rpx 20rpx;
  border-radius: 50rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.modern-action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 联系客户按钮 - 粉色渐变 */
.modern-action-btn.contact-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff4081);
  color: white;
}

/* 修改上门时间按钮 - 蓝色渐变 */
.modern-action-btn.reschedule-btn {
  background: linear-gradient(135deg, #4285f4, #1976d2);
  color: white;
}

/* 查看评价按钮 - 橙色渐变 */
.modern-action-btn.review-btn {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
}

.btn-icon {
  font-size: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
  width: 36rpx;
  height: 36rpx;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.more-btn {
  margin-right: 20rpx;
  font-size: 32rpx;
  position: relative;
  color: rgba(102, 102, 102, 1);
}
.more-actions-dropdown {
  position: absolute;
  bottom: 110rpx;
  left: 0;
  width: 260rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
}

.dropdown-item {
  padding: 20rpx;
  text-align: center;
  font-size: 24rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
  transition: background-color 0.3s;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f4f4f4;
}

.hidden {
  display: none;
}

/* 为底部按钮留出空间 */
.container {
  padding-bottom: 180rpx;
}

/* 原价样式 */
.original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 28rpx;
}

/* 追加服务样式 */
.additional-services,
.all-additional-services {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.service-item {
  border: 1rpx solid #e8e8e8;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.service-item:last-child {
  margin-bottom: 0;
}

.service-item:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.service-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.service-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.service-status.pending {
  background: #ff9500;
}

.service-status.confirmed {
  background: #4CAF50;
}

.service-status.rejected {
  background: #f44336;
}

.service-status.pending_payment {
  background: #FF5722;
}

.service-status.paid {
  background: #34c759;
}

.service-status.completed {
  background: #2196F3;
}

.service-status.cancelled {
  background: #9E9E9E;
}

.service-status.refunding {
  background: #FF9800;
}

.service-status.refunded {
  background: #607D8B;
}

/* 客户信息样式 */
.service-customer-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.customer-label {
  font-size: 26rpx;
  color: #666;
  /* width: 5em; */
  flex-shrink: 0;
  /* text-align: right; */
}

.customer-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-right: 8rpx;
}

.customer-phone {
  font-size: 24rpx;
  color: #999;
}

/* 服务详情样式 */
.service-details {
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  /* width: 5em; */
  flex-shrink: 0;
  /* text-align: right; */
}

.detail-value {
  font-size: 26rpx;
  color: #333;
}

.detail-time {
  color: #999;
  font-size: 24rpx;
}

/* 价格信息样式 */
.service-price-info {
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  background: #fff8f0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff9500;
}

.price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4rpx;
}

.price-row:last-child {
  margin-bottom: 0;
}

.price-label {
  font-size: 26rpx;
  color: #666;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.current-price {
  font-size: 28rpx;
  color: #ff4444;
  font-weight: bold;
}



.service-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
  margin-top: 20rpx;
}

/* 追加服务操作按钮 - 与系统按钮风格保持一致 */
.service-action-btn {
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  text-align: center;
  min-width: 120rpx;
  transition: all 0.2s ease;
  cursor: pointer;
}

/* 确认按钮 - 使用系统主色调 */
.service-action-btn.confirm-btn {
  background: rgba(47, 131, 255, 1);
  color: white;
}

.service-action-btn.confirm-btn:active {
  background: rgba(47, 131, 255, 0.8);
  transform: scale(0.95);
}

/* 拒绝按钮 - 使用灰色调 */
.service-action-btn.reject-btn {
  background: rgba(238, 238, 238, 1);
  color: rgba(51, 51, 51, 1);
}

.service-action-btn.reject-btn:active {
  background: rgba(220, 220, 220, 1);
  transform: scale(0.95);
}



/* 拒绝原因模态框样式 */
.reject-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(2rpx);
}

.modal-content {
  position: relative;
  width: 640rpx;
  max-width: 90vw;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  padding: 40rpx 32rpx 24rpx;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  letter-spacing: 1rpx;
}

.modal-body {
  padding: 32rpx;
}

.reject-textarea {
  width: 100%;
  min-height: 240rpx;
  padding: 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.6;
  box-sizing: border-box;
  background: #fafbfc;
  transition: all 0.2s ease;
}

.reject-textarea:focus {
  border-color: rgba(47, 131, 255, 0.6);
  background: #fff;
  box-shadow: 0 0 0 4rpx rgba(47, 131, 255, 0.1);
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
  background: #fafbfc;
}

.modal-btn {
  flex: 1;
  font-size: 28rpx;
  border: none;
  border-radius: 40rpx;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
  font-weight: 500;
}

.modal-btn.cancel-btn {
  background: rgba(238, 238, 238, 1);
  color: rgba(51, 51, 51, 1);
}

.modal-btn.cancel-btn:active {
  background: rgba(220, 220, 220, 1);
  transform: scale(0.95);
}

.modal-btn.confirm-btn {
  background: rgba(255, 68, 68, 1);
  color: white;
  font-weight: 600;
}

.modal-btn.confirm-btn:active {
  background: rgba(255, 68, 68, 0.8);
  transform: scale(0.95);
}

/* 拒绝原因样式 */
.reject-reason {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #fff5f5;
  border-radius: 8rpx;
  border-left: 4rpx solid #f44336;
}

.reason-label {
  font-size: 26rpx;
  color: #666;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.reason-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 特殊情况说明样式 */
.special-note-section,
.special-note-entry {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
}

.special-note-content {
  border: 1rpx solid #e8e8e8;
  border-radius: 16rpx;
  padding: 24rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.note-info {
  margin-bottom: 20rpx;
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.note-employee {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.note-time {
  font-size: 24rpx;
  color: #999;
}

.note-text {
  margin-bottom: 16rpx;
}

.content-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-all;
}

.note-photos {
  margin-top: 16rpx;
}

.photos-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.note-photo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  object-fit: cover;
  border: 1rpx solid #e8e8e8;
}

.note-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
}

.note-action-btn {
  padding: 12rpx 24rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  text-align: center;
  min-width: 120rpx;
  background: rgba(255, 152, 0, 1);
  color: white;
  transition: all 0.2s ease;
}

.note-action-btn:active {
  background: rgba(255, 152, 0, 0.8);
  transform: scale(0.95);
}

.note-action-btn.view-btn {
  background: rgba(47, 131, 255, 1);
}

.note-action-btn.view-btn:active {
  background: rgba(47, 131, 255, 0.8);
}

/* 特殊情况说明入口样式 */
.entry-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  background: #fafafa;
  transition: all 0.2s ease;
}

.entry-content:active {
  background: #f0f0f0;
  border-color: #ccc;
}

.entry-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.entry-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
}

.entry-arrow {
  font-size: 24rpx;
  color: #999;
}

/* 地址容器样式 */
.address-container {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  transition: opacity 0.2s ease;
  min-width: 0;
}

.address-container:active {
  opacity: 0.7;
}

.address-container .content {
  flex: 1;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
}

/* 用户备注样式 */
.user-remark {
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
  color: #333;
  font-size: 28rpx;
}

.navigation-icon {
  margin-left: 8rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  margin-top: 2rpx;
}
