// pages/mine/promotion/index.js
import promotionApi from '../../../api/modules/promotion';
import userApi from '../../../api/modules/user';
import Session from '../../../common/Session';

Page({
  data: {
    userInfo: null,
    promotionCode: '', // 推广码
    statistics: {
      totalPromoted: 0,
      monthlyPromoted: 0
    },
    loading: false,
    showModal: false,
    modalTitle: '',
    modalContent: ''
  },

  onLoad() {
    this.setData({
      userInfo: Session.getUser()
    });
    this.loadUserInfo();
    this.loadStatistics();
  },

  onShow() {
    // 页面显示时刷新统计数据和用户信息
    this.loadUserInfo();
    this.loadStatistics();
  },

  /**
   * 加载用户信息（包括推广码）
   */
  async loadUserInfo() {
    const userInfo = this.data.userInfo;
    if (!userInfo || !userInfo.id) {
      return;
    }

    try {
      // 获取最新的员工详细信息
      const employeeInfo = await userApi.getEmployeeInfo(userInfo.id);

      if (employeeInfo) {
        // 更新用户信息
        const newUserInfo = { ...userInfo, ...employeeInfo };
        Session.setUser(newUserInfo);
        this.setData({
          userInfo: newUserInfo,
          promotionCode: employeeInfo.promotionCode || ''
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      // 如果获取失败，尝试从当前用户信息中获取推广码
      const currentUserInfo = this.data.userInfo;
      if (currentUserInfo && currentUserInfo.promotionCode) {
        this.setData({ promotionCode: currentUserInfo.promotionCode });
      }
    }
  },

  /**
   * 加载推广统计数据
   */
  async loadStatistics() {
    const userInfo = this.data.userInfo;
    if (!userInfo || !userInfo.id) {
      return;
    }

    try {
      this.setData({ loading: true });
      const statistics = await promotionApi.getStatistics(userInfo.id);
      this.setData({ statistics });
    } catch (error) {
      console.error('加载推广统计失败:', error);
      this.showToast('加载统计数据失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 复制推广码
   */
  copyPromotionCode() {
    const { promotionCode } = this.data;
    if (!promotionCode) {
      this.showToast('暂无推广码，请联系管理员');
      return;
    }

    wx.setClipboardData({
      data: promotionCode,
      success: () => {
        this.showToast('推广码已复制到剪贴板');
      },
      fail: () => {
        this.showToast('复制失败，请重试');
      }
    });
  },



  /**
   * 查看推广用户列表
   */
  viewCustomers() {
    wx.navigateTo({
      url: '/pages/mine/promotion/customers/index'
    });
  },

  /**
   * 显示提示信息
   */
  showToast(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 显示模态框
   */
  showModal(title, content) {
    this.setData({
      showModal: true,
      modalTitle: title,
      modalContent: content
    });
  },

  /**
   * 模态框确认
   */
  onModalConfirm() {
    this.setData({ showModal: false });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadUserInfo();
    this.loadStatistics();
    wx.stopPullDownRefresh();
  }
});
