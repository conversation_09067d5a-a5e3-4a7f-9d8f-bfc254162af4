.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

/* 搜索容器 */
.search-container {
  display: flex;
  gap: 20rpx;
  align-items: center;
  margin-bottom: 30rpx;
}

.search-box {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 25rpx;
  padding: 0 20rpx;
  height: 60rpx;
  transition: all 0.3s ease;
}

.search-box:focus-within {
  border-color: #5470c6;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(84, 112, 198, 0.1);
}

.search-icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 15rpx;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

.clear-icon {
  font-size: 24rpx;
  color: #999;
  padding: 10rpx;
  margin-left: 10rpx;
}

.search-btn {
  background: linear-gradient(135deg, #5470c6 0%, #4c63d2 100%);
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 10rpx 30rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(84, 112, 198, 0.3);
  transition: all 0.3s ease;
}

.search-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.3);
}

/* 日期筛选容器 */
.date-filter-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.date-filter-row {
  display: flex;
  gap: 20rpx;
}

.date-picker-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.date-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  height: 60rpx;
  transition: all 0.3s ease;
}

.date-picker.has-value {
  background: #e8f2ff;
  border-color: #5470c6;
}

.date-picker:active {
  transform: scale(0.98);
}

.date-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.date-picker:not(.has-value) .date-text {
  color: #999;
}

.date-icon {
  font-size: 24rpx;
  color: #5470c6;
}

/* 筛选操作 */
.filter-actions {
  display: flex;
  justify-content: center;
  margin-top: 10rpx;
}

.clear-filter-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #fff5f5;
  border: 2rpx solid #fed7d7;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  color: #e53e3e;
  transition: all 0.3s ease;
}

.clear-filter-btn:active {
  background: #fed7d7;
  transform: scale(0.95);
}

.clear-icon {
  font-size: 20rpx;
}

/* 列表区域 */
.list-section {
  flex: 1;
  padding: 0 30rpx;
  margin-top: 10rpx;
}

.empty-state {
  text-align: center;
  padding: 120rpx 0;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
}

.checkin-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 打卡卡片样式 */
.checkin-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: #fafbfc;
  border-bottom: 1rpx solid #f0f0f0;
}

.time-info {
  flex: 1;
}

.time-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.delete-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
  height: 60rpx;
  transition: all 0.3s ease;
}

.delete-btn:active {
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(255, 107, 107, 0.3);
}

.card-content {
  padding: 30rpx;
}

.info-row {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
  font-weight: 500;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  line-height: 1.5;
}

.info-value.location {
  color: #5470c6;
}

/* 照片区域 */
.photos-section {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 分组照片 */
.photo-group {
  margin-bottom: 20rpx;
}

.photo-group:last-child {
  margin-bottom: 0;
}

.group-title {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.photos-grid {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.photo-item {
  width: 220rpx;
  height: 220rpx;
  border-radius: 8rpx;
  border: 1rpx solid #f0f0f0;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 26rpx;
}

.no-more {
  text-align: center;
  padding: 60rpx 0;
  color: #ccc;
  font-size: 24rpx;
}
