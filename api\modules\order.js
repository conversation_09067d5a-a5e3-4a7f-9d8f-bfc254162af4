import request, { analysisRes } from '../request';
import config from '../config';

const { order, payment } = config.apiUrls;

export default {
  /** 查询可接单列表 */
  async list(userId, type) {
    const res = await request.get(order.list.replace('{userId}', userId), {
      hideLoading: true,
      type,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 按状态查询员工名下的订单列表 */
  async myList(employeeId, status) {
    const where = {
      hideLoading: true,
    };
    if (status && status !== 'all') {
      where.status = status;
    }
    const res = await request.get(order.myList.replace('{employeeId}', employeeId), where);
    const data = analysisRes(res);
    return data;
  },

  /** 接单 */
  async accept(orderId, employeeId) {
    const res = await request.post(order.accept.replace('{orderId}', orderId), {
      employeeId,
    });
    const data = analysisRes(res);
    return data;
  },

  // 修改服务时间
  async updateServiceTime(orderId, employeeId, serviceTime) {
    const res = await request.put(order.updateServiceTime.replace('{orderId}', orderId), {
      employeeId,
      serviceTime,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 出发 */
  async dispatch(orderId, employeeId) {
    const res = await request.post(order.dispatch.replace('{orderId}', orderId), {
      employeeId,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 开始服务 */
  async start(orderId, employeeId, beforePhotos = []) {
    const res = await request.post(order.start.replace('{orderId}', orderId), {
      employeeId,
      beforePhotos,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 完成订单 */
  async complete(orderId, employeeId, afterPhotos = []) {
    const res = await request.post(order.complete.replace('{orderId}', orderId), {
      employeeId,
      afterPhotos,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 批量上传服务前照片 */
  async uploadBeforePhotos(orderId, employeeId, photoUrls) {
    const res = await request.post(order.uploadBeforePhotos.replace('{orderId}', orderId), {
      employeeId,
      photoUrls,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 批量上传服务后照片 */
  async uploadAfterPhotos(orderId, employeeId, photoUrls) {
    const res = await request.post(order.uploadAfterPhotos.replace('{orderId}', orderId), {
      employeeId,
      photoUrls,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 查询订单服务照片 */
  async getServicePhotos(orderId) {
    const res = await request.get(order.getServicePhotos.replace('{orderId}', orderId), {
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 查询员工待确认的追加服务列表 */
  async getPendingAdditionalServices(employeeId, current = 1, pageSize = 10) {
    const res = await request.get(order.getPendingAdditionalServices, {
      employeeId,
      current,
      pageSize,
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 查询指定订单详情的追加服务列表 */
  async getAdditionalServices(orderDetailId, status = null) {
    const params = {
      hideLoading: true,
    };
    if (status) {
      params.status = status;
    }
    const res = await request.get(
      order.getAdditionalServices.replace('{orderDetailId}', orderDetailId),
      params
    );
    const data = analysisRes(res);
    return data;
  },

  /** 员工确认追加服务 */
  async confirmAdditionalService(orderDetailId, additionalServiceId, employeeId) {
    const res = await request.post(
      order.confirmAdditionalService
        .replace('{orderDetailId}', orderDetailId)
        .replace('{id}', additionalServiceId),
      {
        employeeId,
      }
    );
    const data = analysisRes(res);
    return data;
  },

  /** 员工拒绝追加服务 */
  async rejectAdditionalService(orderDetailId, additionalServiceId, employeeId, rejectReason) {
    const res = await request.post(
      order.rejectAdditionalService
        .replace('{orderDetailId}', orderDetailId)
        .replace('{id}', additionalServiceId),
      {
        employeeId,
        rejectReason,
      }
    );
    const data = analysisRes(res);
    return data;
  },

  /** 获取追加服务支付参数 */
  async getAdditionalServicePayParams(orderDetailId, additionalServiceId) {
    console.log('获取追加服务支付参数 - 开始', { orderDetailId, additionalServiceId });
    const res = await request.get(
      payment.getAdditionalServicePayParams
        .replace('{orderDetailId}', orderDetailId)
        .replace('{id}', additionalServiceId),
      {
        hideLoading: false,
      }
    );
    const data = analysisRes(res);
    console.log('获取追加服务支付参数 - 结果', data);
    return data;
  },

  /** 确认追加服务支付 */
  async confirmAdditionalServicePayment(orderDetailId, additionalServiceId, paymentData) {
    console.log('确认追加服务支付 - 开始', { orderDetailId, additionalServiceId, paymentData });
    const res = await request.post(
      payment.confirmAdditionalServicePayment
        .replace('{orderDetailId}', orderDetailId)
        .replace('{id}', additionalServiceId),
      paymentData
    );
    const data = analysisRes(res);
    console.log('确认追加服务支付 - 结果', data);
    return data;
  },

  /** 查询追加服务支付状态 */
  async getAdditionalServicePayStatus(orderDetailId, additionalServiceId) {
    console.log('查询追加服务支付状态 - 开始', { orderDetailId, additionalServiceId });
    const res = await request.get(
      payment.getAdditionalServicePayStatus
        .replace('{orderDetailId}', orderDetailId)
        .replace('{id}', additionalServiceId),
      {
        hideLoading: true,
      }
    );
    const data = analysisRes(res);
    console.log('查询追加服务支付状态 - 结果', data);
    return data;
  },
};
