/* pages/mine/promotion/customers/index.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 160rpx;
}

/* 统计头部 */
.stats-header {
  background-color: white;
  padding: 30rpx 24rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.stats-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.stats-count {
  font-size: 28rpx;
  color: #666;
}

/* 客户列表 */
.customer-list {
  padding: 0 24rpx;
}

.customer-item {
  background-color: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.05);
}

.customer-info {
  display: flex;
  align-items: flex-start;
}

.customer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.customer-details {
  flex: 1;
}

.customer-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.customer-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.member-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  background-color: #f0f0f0;
  border-radius: 12rpx;
}

.customer-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.customer-phone {
  font-size: 28rpx;
  color: #666;
}

.join-time {
  font-size: 24rpx;
  color: #999;
}

.customer-points {
  display: flex;
  align-items: center;
}

.points-label {
  font-size: 24rpx;
  color: #999;
}

.points-value {
  font-size: 24rpx;
  color: #2f83ff;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #2f83ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.no-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}
