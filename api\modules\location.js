import request from "../request";
import config from "../config";

const { location } = config.apiUrls;

export default {
  /**
   * 员工端上报当前位置
   * @param {number} employeeId 员工ID
   * @param {number} latitude 纬度（-90到90之间）
   * @param {number} longitude 经度（-180到180之间）
   * @param {string} address 当前地址描述（可选）
   */
  async updateEmployeeLocation(employeeId, latitude, longitude, address = '') {
    try {
      const res = await request.post(location.updateEmployeeLocation, {
        employeeId,
        latitude,
        longitude,
        address,
        hideLoading: true, // 隐藏loading提示
      });
      // 静默处理响应，不显示错误提示
      const { errCode, msg, data } = res || {};

      if (errCode === 0) {
        // 成功
        console.log('LocationManager API: 位置上报成功', data);
        return data;
      } else {
        // 业务错误（如员工未绑定车辆），静默处理，不影响位置服务运行
        console.log('LocationManager API: 位置上报业务错误，但继续运行', { errCode, msg });
        return { success: true, message: '位置信息已上报（业务处理中）' };
      }
    } catch (error) {
      // 静默处理网络错误
      console.log('LocationManager API: 位置上报网络错误', error.message || error);
      return null;
    }
  },
};
