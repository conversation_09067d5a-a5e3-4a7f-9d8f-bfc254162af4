// pages/message/messageDetail/index.js
import utils from '../../utils/util';
import messageApi from '../../../api/modules/message';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    messageId: '',
    messageDetail: null,
    loading: true,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ messageId: id });
      this.loadMessageDetail();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载消息详情
   */
  async loadMessageDetail() {
    try {
      const res = await messageApi.getMessageDetail(this.data.messageId);
      if (res) {
        const messageDetail = {
          ...res,
          time: utils.formatNormalDate(res.createdAt),
          icon: this.getMessageIcon(res.type),
          parsedExtraData: this.parseExtraData(res.extraData)
        };
        this.setData({ messageDetail });

        // 如果消息未读，自动标记为已读
        if (!res.isRead) {
          this.markAsRead();
        }
      }
    } catch (error) {
      console.error('获取消息详情失败:', error);
      wx.showToast({
        title: '获取消息详情失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 标记消息为已读
   */
  async markAsRead() {
    try {
      await messageApi.markAsRead(this.data.messageId);
      // 更新本地数据
      const messageDetail = { ...this.data.messageDetail };
      messageDetail.isRead = true;
      messageDetail.readAt = new Date().toISOString();
      this.setData({ messageDetail });
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  },

  /**
   * 解析扩展数据
   */
  parseExtraData(extraData) {
    if (!extraData) return null;

    try {
      // 如果是字符串，尝试解析JSON
      let data = extraData;
      if (typeof extraData === 'string') {
        data = JSON.parse(extraData);
      }

      // 如果解析后不是对象，返回null
      if (typeof data !== 'object' || data === null) {
        return null;
      }

      // 转换为显示格式
      const displayData = [];
      const fieldMap = {
        orderId: '订单ID',
        orderNo: '订单号',
        customerName: '客户姓名',
        serviceTime: '服务时间',
        serviceAddress: '服务地址',
        petInfo: '宠物信息',
        amount: '金额',
        remark: '备注'
      };

      for (const [key, value] of Object.entries(data)) {
        if (value !== null && value !== undefined && value !== '') {
          const label = fieldMap[key] || key;
          let displayValue = value;

          // 特殊处理时间格式
          if (key === 'serviceTime' && value) {
            displayValue = utils.formatNormalDate(value);
          }

          displayData.push({
            label,
            value: displayValue
          });
        }
      }

      return displayData.length > 0 ? displayData : null;
    } catch (error) {
      console.error('解析扩展数据失败:', error);
      return null;
    }
  },

  /**
   * 根据消息类型获取图标
   */
  getMessageIcon(type) {
    const iconMap = {
      'system': '//xian7.zos.ctyun.cn/pet/static/msg1.png',
      'platform': '//xian7.zos.ctyun.cn/pet/static/msg2.png',
      'order': '//xian7.zos.ctyun.cn/pet/static/msg1.png',
    };
    return iconMap[type] || '//xian7.zos.ctyun.cn/pet/static/msg1.png';
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadMessageDetail().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '消息详情',
      path: `/pages/message/messageDetail/index?id=${this.data.messageId}`
    };
  }
})