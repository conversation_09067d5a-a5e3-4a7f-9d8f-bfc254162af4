// components/custom-picker/index.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    selectedTime: {
      type: String,
      value: ''
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 新增的时间选择器数据
    timeArray: [0, 0, 0, 0],
    timeRange: [
      [], // 年份
      [], // 月份
      [], // 日期
      [] // 时间（半小时间隔）
    ],
    selectedTime: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    initTimePicker(keepSelection = false) {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;
      const currentDay = currentDate.getDate();
      const currentHours = currentDate.getHours();
      const currentMinutes = currentDate.getMinutes();

      // 保存当前选择的值（如果有）
      let selectedYearValue, selectedMonthValue, selectedDayValue, selectedTimeValue;
      if (keepSelection && this.data.timeRange[0].length > 0) {
        const [yearIndex, monthIndex, dayIndex, timeIndex] = this.data.timeArray;
        if (yearIndex < this.data.timeRange[0].length) {
          selectedYearValue = this.data.timeRange[0][yearIndex];
        }
        if (monthIndex < this.data.timeRange[1].length) {
          selectedMonthValue = this.data.timeRange[1][monthIndex];
        }
        if (dayIndex < this.data.timeRange[2].length) {
          selectedDayValue = this.data.timeRange[2][dayIndex];
        }
        if (timeIndex < this.data.timeRange[3].length) {
          selectedTimeValue = this.data.timeRange[3][timeIndex];
        }
      }

      // 生成年份范围（当前年份到未来2年）
      const years = [];
      for (let year = currentYear; year <= currentYear + 2; year++) {
        years.push(year);
      }

      // 确定选择的年份
      const selectedYear = selectedYearValue || years[0];
      const yearIndex = years.findIndex(y => y === selectedYear);

      // 生成月份范围
      const months = [];
      // 如果是当前年份，则从当前月份开始
      if (selectedYear === currentYear) {
        for (let month = currentMonth; month <= 12; month++) {
          months.push(month);
        }
      } else {
        // 其他年份显示所有月份
        for (let month = 1; month <= 12; month++) {
          months.push(month);
        }
      }

      // 确定选择的月份
      let selectedMonth;
      if (selectedMonthValue && months.includes(selectedMonthValue)) {
        selectedMonth = selectedMonthValue;
      } else {
        selectedMonth = months[0];
      }
      const monthIndex = months.findIndex(m => m === selectedMonth);

      // 生成日期范围
      const days = [];
      // 获取选定月份的天数
      const daysInMonth = this.getDaysInMonth(selectedYear, selectedMonth);

      // 如果是当前年月，则从当前日期开始
      if (selectedYear === currentYear && selectedMonth === currentMonth) {
        for (let day = currentDay; day <= daysInMonth; day++) {
          days.push(day);
        }
      } else {
        // 其他月份显示所有日期
        for (let day = 1; day <= daysInMonth; day++) {
          days.push(day);
        }
      }

      // 确定选择的日期
      let selectedDay;
      if (selectedDayValue && days.includes(selectedDayValue)) {
        selectedDay = selectedDayValue;
      } else {
        selectedDay = days[0];
      }
      const dayIndex = days.findIndex(d => d === selectedDay);

      // 生成半小时间间隔（限制在早上7点到晚上10点）
      const times = [];
      // 设置时间范围
      const startHour = 7; // 早上7点
      const endHour = 22;  // 晚上10点

      // 如果是当前年月日，则从下一个半小时开始
      if (selectedYear === currentYear &&
          selectedMonth === currentMonth &&
          selectedDay === currentDay) {
        // 获取下一个半小时
        const nextHalfHour = this.getNextHalfHour(currentHours, currentMinutes);
        const [nextHour, nextMinute] = nextHalfHour.split(':').map(Number);

        // 确定开始时间（当前时间和设定的开始时间中的较晚者）
        const effectiveStartHour = Math.max(nextHour, startHour);

        // 只添加在有效范围内的时间
        for (let hour = effectiveStartHour; hour <= endHour; hour++) {
          if (hour === nextHour && hour >= startHour) {
            // 对于当前小时，只添加从下一个半小时开始的时间
            if (nextMinute === 0) {
              times.push(`${hour.toString().padStart(2, '0')}:00`);
              times.push(`${hour.toString().padStart(2, '0')}:30`);
            } else {
              times.push(`${hour.toString().padStart(2, '0')}:30`);
            }
          } else {
            // 对于其他小时，添加所有半小时间隔
            times.push(`${hour.toString().padStart(2, '0')}:00`);
            times.push(`${hour.toString().padStart(2, '0')}:30`);
          }
        }
      } else {
        // 其他日期显示7点到22点的时间
        for (let hour = startHour; hour <= endHour; hour++) {
          times.push(`${hour.toString().padStart(2, '0')}:00`);
          times.push(`${hour.toString().padStart(2, '0')}:30`);
        }
      }

      // 确定选择的时间
      let timeIndex;
      if (selectedTimeValue && times.includes(selectedTimeValue)) {
        timeIndex = times.findIndex(t => t === selectedTimeValue);
      } else {
        timeIndex = 0;
      }

      // 更新数据
      this.setData({
        timeRange: [years, months, days, times],
        timeArray: [
          yearIndex >= 0 ? yearIndex : 0,
          monthIndex >= 0 ? monthIndex : 0,
          dayIndex >= 0 ? dayIndex : 0,
          timeIndex >= 0 ? timeIndex : 0
        ]
      });

      // 初始化后立即更新一次选中的时间
      this.updateSelectedTime();
    },

    // 获取月份的天数
    getDaysInMonth(year, month) {
      return new Date(year, month, 0).getDate();
    },

    // 获取下一个半小时间隔
    getNextHalfHour(hours, minutes) {
      // 如果当前分钟小于30，则下一个半小时是当前小时的30分
      // 否则，下一个半小时是下一个小时的00分
      if (minutes < 30) {
        return `${hours.toString().padStart(2, '0')}:30`;
      } else {
        const nextHours = hours + 1;
        return `${nextHours.toString().padStart(2, '0')}:00`;
      }
    },

    // 更新选中的时间
    updateSelectedTime() {
      const [yearIndex, monthIndex, dayIndex, timeIndex] = this.data.timeArray;

      // 确保索引有效
      if (this.data.timeRange[0].length <= yearIndex ||
          this.data.timeRange[1].length <= monthIndex ||
          this.data.timeRange[2].length <= dayIndex ||
          this.data.timeRange[3].length <= timeIndex) {
        return;
      }

      const selectedYear = this.data.timeRange[0][yearIndex];
      const selectedMonth = this.data.timeRange[1][monthIndex];
      const selectedDay = this.data.timeRange[2][dayIndex];
      const selectedTime = this.data.timeRange[3][timeIndex];

      if (!selectedYear || !selectedMonth || !selectedDay || !selectedTime) {
        return;
      }

      const formattedTime = `${selectedYear}-${selectedMonth.toString().padStart(2, '0')}-${selectedDay.toString().padStart(2, '0')} ${selectedTime}`;

      this.setData({
        selectedTime: formattedTime
      });
    },

    // 时间选择器变化
    bindTimeChange(e) {
      const value = e.detail.value;

      // 更新选择器的值
      this.setData({
        timeArray: value
      }, () => {
        // 重新初始化时间选择器，但保持当前选择
        this.initTimePicker(true);
      });
    },

    // 处理列变化，用于动态更新日期
    bindTimeColumnChange(e) {
      // 当列变化时，我们更新对应的值，然后重新初始化时间选择器
      const { column, value } = e.detail;
      const newTimeArray = [...this.data.timeArray];
      newTimeArray[column] = value;

      this.setData({
        timeArray: newTimeArray
      }, () => {
        // 重新初始化时间选择器，但保持当前选择
        this.initTimePicker(true);
      });
    },

    // 确认选择时间
    onConfirm() {
      if (!this.data.selectedTime) {
        wx.showToast({
          title: '请先选择时间',
          icon: 'none'
        });
        return;
      }

      // 触发确认事件，将选择的时间传递给父组件
      this.triggerEvent('confirm', this.data.selectedTime);
    },

    // 取消选择
    onCancel() {
      this.triggerEvent('cancel');
    },
  },
  /**
   * 组件生命周期函数，在组件实例进入页面节点树时执行
   */
  attached() {
    // 初始化时间选择器，不保持选择（因为还没有选择）
    this.initTimePicker(false);
  },

  /**
   * 组件生命周期函数，在组件布局完成后执行
   */
  ready() {
  }
})