@charset "UTF-8";
/**
 * 修改common/diygw-ui/index.scss 可以取消相关组件样式
 * 修改common/diygw-ui/iconfont.scss 如果发布APP可能会图标字体找不到的问题，加上https测试
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*!
 * animate.css - https://animate.style/
 * Version - 4.1.1
 * Licensed under the MIT license - http://opensource.org/licenses/MIT
 *
 * Copyright (c) 2020 Animate.css
 */
page{
  --animate-duration: 1s;
  --animate-delay: 1s;
  --animate-repeat: 1;
}
.animate__animated {
  animation-duration: 1s;
  animation-duration: var(--animate-duration);
  animation-fill-mode: both;
}
.animate__animated.animate__infinite {
  animation-iteration-count: infinite;
}
.animate__animated.animate__repeat-1 {
  animation-iteration-count: 1;
  animation-iteration-count: var(--animate-repeat);
}
.animate__animated.animate__repeat-2 {
  animation-iteration-count: calc(1 * 2);
  animation-iteration-count: calc(var(--animate-repeat) * 2);
}
.animate__animated.animate__repeat-3 {
  animation-iteration-count: calc(1 * 3);
  animation-iteration-count: calc(var(--animate-repeat) * 3);
}
.animate__animated.animate__delay-1s {
  animation-delay: 1s;
  animation-delay: var(--animate-delay);
}
.animate__animated.animate__delay-2s {
  animation-delay: calc(1s * 2);
  animation-delay: calc(var(--animate-delay) * 2);
}
.animate__animated.animate__delay-3s {
  animation-delay: calc(1s * 3);
  animation-delay: calc(var(--animate-delay) * 3);
}
.animate__animated.animate__delay-4s {
  animation-delay: calc(1s * 4);
  animation-delay: calc(var(--animate-delay) * 4);
}
.animate__animated.animate__delay-5s {
  animation-delay: calc(1s * 5);
  animation-delay: calc(var(--animate-delay) * 5);
}
.animate__animated.animate__faster {
  animation-duration: calc(1s / 2);
  animation-duration: calc(var(--animate-duration) / 2);
}
.animate__animated.animate__fast {
  animation-duration: calc(1s * 0.8);
  animation-duration: calc(var(--animate-duration) * 0.8);
}
.animate__animated.animate__slow {
  animation-duration: calc(1s * 2);
  animation-duration: calc(var(--animate-duration) * 2);
}
.animate__animated.animate__slower {
  animation-duration: calc(1s * 3);
  animation-duration: calc(var(--animate-duration) * 3);
}
@media print, (prefers-reduced-motion: reduce) {
.animate__animated {
    animation-duration: 1ms !important;
    transition-duration: 1ms !important;
    animation-iteration-count: 1 !important;
}
.animate__animated[class*='Out'] {
    opacity: 0;
}
}
/* Attention seekers  */
@keyframes bounce {
from,
  20%,
  53%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0);
}
40%,
  43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -30px, 0) scaleY(1.1);
}
70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -15px, 0) scaleY(1.05);
}
80% {
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0) scaleY(0.95);
}
90% {
    transform: translate3d(0, -4px, 0) scaleY(1.02);
}
}
.animate__bounce {
  animation-name: bounce;
  transform-origin: center bottom;
}
@keyframes flash {
from,
  50%,
  to {
    opacity: 1;
}
25%,
  75% {
    opacity: 0;
}
}
.animate__flash {
  animation-name: flash;
}
/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@keyframes pulse {
from {
    transform: scale3d(1, 1, 1);
}
50% {
    transform: scale3d(1.05, 1.05, 1.05);
}
to {
    transform: scale3d(1, 1, 1);
}
}
.animate__pulse {
  animation-name: pulse;
  animation-timing-function: ease-in-out;
}
@keyframes rubberBand {
from {
    transform: scale3d(1, 1, 1);
}
30% {
    transform: scale3d(1.25, 0.75, 1);
}
40% {
    transform: scale3d(0.75, 1.25, 1);
}
50% {
    transform: scale3d(1.15, 0.85, 1);
}
65% {
    transform: scale3d(0.95, 1.05, 1);
}
75% {
    transform: scale3d(1.05, 0.95, 1);
}
to {
    transform: scale3d(1, 1, 1);
}
}
.animate__rubberBand {
  animation-name: rubberBand;
}
@keyframes shakeX {
from,
  to {
    transform: translate3d(0, 0, 0);
}
10%,
  30%,
  50%,
  70%,
  90% {
    transform: translate3d(-10px, 0, 0);
}
20%,
  40%,
  60%,
  80% {
    transform: translate3d(10px, 0, 0);
}
}
.animate__shakeX {
  animation-name: shakeX;
}
@keyframes shakeY {
from,
  to {
    transform: translate3d(0, 0, 0);
}
10%,
  30%,
  50%,
  70%,
  90% {
    transform: translate3d(0, -10px, 0);
}
20%,
  40%,
  60%,
  80% {
    transform: translate3d(0, 10px, 0);
}
}
.animate__shakeY {
  animation-name: shakeY;
}
@keyframes headShake {
0% {
    transform: translateX(0);
}
6.5% {
    transform: translateX(-6px) rotateY(-9deg);
}
18.5% {
    transform: translateX(5px) rotateY(7deg);
}
31.5% {
    transform: translateX(-3px) rotateY(-5deg);
}
43.5% {
    transform: translateX(2px) rotateY(3deg);
}
50% {
    transform: translateX(0);
}
}
.animate__headShake {
  animation-timing-function: ease-in-out;
  animation-name: headShake;
}
@keyframes swing {
20% {
    transform: rotate3d(0, 0, 1, 15deg);
}
40% {
    transform: rotate3d(0, 0, 1, -10deg);
}
60% {
    transform: rotate3d(0, 0, 1, 5deg);
}
80% {
    transform: rotate3d(0, 0, 1, -5deg);
}
to {
    transform: rotate3d(0, 0, 1, 0deg);
}
}
.animate__swing {
  transform-origin: top center;
  animation-name: swing;
}
@keyframes tada {
from {
    transform: scale3d(1, 1, 1);
}
10%,
  20% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
}
30%,
  50%,
  70%,
  90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
}
40%,
  60%,
  80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
}
to {
    transform: scale3d(1, 1, 1);
}
}
.animate__tada {
  animation-name: tada;
}
/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@keyframes wobble {
from {
    transform: translate3d(0, 0, 0);
}
15% {
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
}
30% {
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
}
45% {
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
}
60% {
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
}
75% {
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
}
to {
    transform: translate3d(0, 0, 0);
}
}
.animate__wobble {
  animation-name: wobble;
}
@keyframes jello {
from,
  11.1%,
  to {
    transform: translate3d(0, 0, 0);
}
22.2% {
    transform: skewX(-12.5deg) skewY(-12.5deg);
}
33.3% {
    transform: skewX(6.25deg) skewY(6.25deg);
}
44.4% {
    transform: skewX(-3.125deg) skewY(-3.125deg);
}
55.5% {
    transform: skewX(1.5625deg) skewY(1.5625deg);
}
66.6% {
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
}
77.7% {
    transform: skewX(0.390625deg) skewY(0.390625deg);
}
88.8% {
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
}
}
.animate__jello {
  animation-name: jello;
  transform-origin: center;
}
@keyframes heartBeat {
0% {
    transform: scale(1);
}
14% {
    transform: scale(1.3);
}
28% {
    transform: scale(1);
}
42% {
    transform: scale(1.3);
}
70% {
    transform: scale(1);
}
}
.animate__heartBeat {
  animation-name: heartBeat;
  animation-duration: calc(1s * 1.3);
  animation-duration: calc(var(--animate-duration) * 1.3);
  animation-timing-function: ease-in-out;
}
/* Back entrances */
@keyframes backInDown {
0% {
    transform: translateY(-1200px) scale(0.7);
    opacity: 0.7;
}
80% {
    transform: translateY(0px) scale(0.7);
    opacity: 0.7;
}
100% {
    transform: scale(1);
    opacity: 1;
}
}
.animate__backInDown {
  animation-name: backInDown;
}
@keyframes backInLeft {
0% {
    transform: translateX(-2000px) scale(0.7);
    opacity: 0.7;
}
80% {
    transform: translateX(0px) scale(0.7);
    opacity: 0.7;
}
100% {
    transform: scale(1);
    opacity: 1;
}
}
.animate__backInLeft {
  animation-name: backInLeft;
}
@keyframes backInRight {
0% {
    transform: translateX(2000px) scale(0.7);
    opacity: 0.7;
}
80% {
    transform: translateX(0px) scale(0.7);
    opacity: 0.7;
}
100% {
    transform: scale(1);
    opacity: 1;
}
}
.animate__backInRight {
  animation-name: backInRight;
}
@keyframes backInUp {
0% {
    transform: translateY(1200px) scale(0.7);
    opacity: 0.7;
}
80% {
    transform: translateY(0px) scale(0.7);
    opacity: 0.7;
}
100% {
    transform: scale(1);
    opacity: 1;
}
}
.animate__backInUp {
  animation-name: backInUp;
}
/* Back exits */
@keyframes backOutDown {
0% {
    transform: scale(1);
    opacity: 1;
}
20% {
    transform: translateY(0px) scale(0.7);
    opacity: 0.7;
}
100% {
    transform: translateY(700px) scale(0.7);
    opacity: 0.7;
}
}
.animate__backOutDown {
  animation-name: backOutDown;
}
@keyframes backOutLeft {
0% {
    transform: scale(1);
    opacity: 1;
}
20% {
    transform: translateX(0px) scale(0.7);
    opacity: 0.7;
}
100% {
    transform: translateX(-2000px) scale(0.7);
    opacity: 0.7;
}
}
.animate__backOutLeft {
  animation-name: backOutLeft;
}
@keyframes backOutRight {
0% {
    transform: scale(1);
    opacity: 1;
}
20% {
    transform: translateX(0px) scale(0.7);
    opacity: 0.7;
}
100% {
    transform: translateX(2000px) scale(0.7);
    opacity: 0.7;
}
}
.animate__backOutRight {
  animation-name: backOutRight;
}
@keyframes backOutUp {
0% {
    transform: scale(1);
    opacity: 1;
}
20% {
    transform: translateY(0px) scale(0.7);
    opacity: 0.7;
}
100% {
    transform: translateY(-700px) scale(0.7);
    opacity: 0.7;
}
}
.animate__backOutUp {
  animation-name: backOutUp;
}
/* Bouncing entrances  */
@keyframes bounceIn {
from,
  20%,
  40%,
  60%,
  80%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}
0% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
}
20% {
    transform: scale3d(1.1, 1.1, 1.1);
}
40% {
    transform: scale3d(0.9, 0.9, 0.9);
}
60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03);
}
80% {
    transform: scale3d(0.97, 0.97, 0.97);
}
to {
    opacity: 1;
    transform: scale3d(1, 1, 1);
}
}
.animate__bounceIn {
  animation-duration: calc(1s * 0.75);
  animation-duration: calc(var(--animate-duration) * 0.75);
  animation-name: bounceIn;
}
@keyframes bounceInDown {
from,
  60%,
  75%,
  90%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}
0% {
    opacity: 0;
    transform: translate3d(0, -3000px, 0) scaleY(3);
}
60% {
    opacity: 1;
    transform: translate3d(0, 25px, 0) scaleY(0.9);
}
75% {
    transform: translate3d(0, -10px, 0) scaleY(0.95);
}
90% {
    transform: translate3d(0, 5px, 0) scaleY(0.985);
}
to {
    transform: translate3d(0, 0, 0);
}
}
.animate__bounceInDown {
  animation-name: bounceInDown;
}
@keyframes bounceInLeft {
from,
  60%,
  75%,
  90%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}
0% {
    opacity: 0;
    transform: translate3d(-3000px, 0, 0) scaleX(3);
}
60% {
    opacity: 1;
    transform: translate3d(25px, 0, 0) scaleX(1);
}
75% {
    transform: translate3d(-10px, 0, 0) scaleX(0.98);
}
90% {
    transform: translate3d(5px, 0, 0) scaleX(0.995);
}
to {
    transform: translate3d(0, 0, 0);
}
}
.animate__bounceInLeft {
  animation-name: bounceInLeft;
}
@keyframes bounceInRight {
from,
  60%,
  75%,
  90%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}
from {
    opacity: 0;
    transform: translate3d(3000px, 0, 0) scaleX(3);
}
60% {
    opacity: 1;
    transform: translate3d(-25px, 0, 0) scaleX(1);
}
75% {
    transform: translate3d(10px, 0, 0) scaleX(0.98);
}
90% {
    transform: translate3d(-5px, 0, 0) scaleX(0.995);
}
to {
    transform: translate3d(0, 0, 0);
}
}
.animate__bounceInRight {
  animation-name: bounceInRight;
}
@keyframes bounceInUp {
from,
  60%,
  75%,
  90%,
  to {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}
from {
    opacity: 0;
    transform: translate3d(0, 3000px, 0) scaleY(5);
}
60% {
    opacity: 1;
    transform: translate3d(0, -20px, 0) scaleY(0.9);
}
75% {
    transform: translate3d(0, 10px, 0) scaleY(0.95);
}
90% {
    transform: translate3d(0, -5px, 0) scaleY(0.985);
}
to {
    transform: translate3d(0, 0, 0);
}
}
.animate__bounceInUp {
  animation-name: bounceInUp;
}
/* Bouncing exits  */
@keyframes bounceOut {
20% {
    transform: scale3d(0.9, 0.9, 0.9);
}
50%,
  55% {
    opacity: 1;
    transform: scale3d(1.1, 1.1, 1.1);
}
to {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
}
}
.animate__bounceOut {
  animation-duration: calc(1s * 0.75);
  animation-duration: calc(var(--animate-duration) * 0.75);
  animation-name: bounceOut;
}
@keyframes bounceOutDown {
20% {
    transform: translate3d(0, 10px, 0) scaleY(0.985);
}
40%,
  45% {
    opacity: 1;
    transform: translate3d(0, -20px, 0) scaleY(0.9);
}
to {
    opacity: 0;
    transform: translate3d(0, 2000px, 0) scaleY(3);
}
}
.animate__bounceOutDown {
  animation-name: bounceOutDown;
}
@keyframes bounceOutLeft {
20% {
    opacity: 1;
    transform: translate3d(20px, 0, 0) scaleX(0.9);
}
to {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0) scaleX(2);
}
}
.animate__bounceOutLeft {
  animation-name: bounceOutLeft;
}
@keyframes bounceOutRight {
20% {
    opacity: 1;
    transform: translate3d(-20px, 0, 0) scaleX(0.9);
}
to {
    opacity: 0;
    transform: translate3d(2000px, 0, 0) scaleX(2);
}
}
.animate__bounceOutRight {
  animation-name: bounceOutRight;
}
@keyframes bounceOutUp {
20% {
    transform: translate3d(0, -10px, 0) scaleY(0.985);
}
40%,
  45% {
    opacity: 1;
    transform: translate3d(0, 20px, 0) scaleY(0.9);
}
to {
    opacity: 0;
    transform: translate3d(0, -2000px, 0) scaleY(3);
}
}
.animate__bounceOutUp {
  animation-name: bounceOutUp;
}
/* Fading entrances  */
@keyframes fadeIn {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.animate__fadeIn {
  animation-name: fadeIn;
}
@keyframes fadeInDown {
from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__fadeInDown {
  animation-name: fadeInDown;
}
@keyframes fadeInDownBig {
from {
    opacity: 0;
    transform: translate3d(0, -2000px, 0);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__fadeInDownBig {
  animation-name: fadeInDownBig;
}
@keyframes fadeInLeft {
from {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__fadeInLeft {
  animation-name: fadeInLeft;
}
@keyframes fadeInLeftBig {
from {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__fadeInLeftBig {
  animation-name: fadeInLeftBig;
}
@keyframes fadeInRight {
from {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__fadeInRight {
  animation-name: fadeInRight;
}
@keyframes fadeInRightBig {
from {
    opacity: 0;
    transform: translate3d(2000px, 0, 0);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__fadeInRightBig {
  animation-name: fadeInRightBig;
}
@keyframes fadeInUp {
from {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__fadeInUp {
  animation-name: fadeInUp;
}
@keyframes fadeInUpBig {
from {
    opacity: 0;
    transform: translate3d(0, 2000px, 0);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__fadeInUpBig {
  animation-name: fadeInUpBig;
}
@keyframes fadeInTopLeft {
from {
    opacity: 0;
    transform: translate3d(-100%, -100%, 0);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__fadeInTopLeft {
  animation-name: fadeInTopLeft;
}
@keyframes fadeInTopRight {
from {
    opacity: 0;
    transform: translate3d(100%, -100%, 0);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__fadeInTopRight {
  animation-name: fadeInTopRight;
}
@keyframes fadeInBottomLeft {
from {
    opacity: 0;
    transform: translate3d(-100%, 100%, 0);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__fadeInBottomLeft {
  animation-name: fadeInBottomLeft;
}
@keyframes fadeInBottomRight {
from {
    opacity: 0;
    transform: translate3d(100%, 100%, 0);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__fadeInBottomRight {
  animation-name: fadeInBottomRight;
}
/* Fading exits */
@keyframes fadeOut {
from {
    opacity: 1;
}
to {
    opacity: 0;
}
}
.animate__fadeOut {
  animation-name: fadeOut;
}
@keyframes fadeOutDown {
from {
    opacity: 1;
}
to {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
}
}
.animate__fadeOutDown {
  animation-name: fadeOutDown;
}
@keyframes fadeOutDownBig {
from {
    opacity: 1;
}
to {
    opacity: 0;
    transform: translate3d(0, 2000px, 0);
}
}
.animate__fadeOutDownBig {
  animation-name: fadeOutDownBig;
}
@keyframes fadeOutLeft {
from {
    opacity: 1;
}
to {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
}
}
.animate__fadeOutLeft {
  animation-name: fadeOutLeft;
}
@keyframes fadeOutLeftBig {
from {
    opacity: 1;
}
to {
    opacity: 0;
    transform: translate3d(-2000px, 0, 0);
}
}
.animate__fadeOutLeftBig {
  animation-name: fadeOutLeftBig;
}
@keyframes fadeOutRight {
from {
    opacity: 1;
}
to {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
}
}
.animate__fadeOutRight {
  animation-name: fadeOutRight;
}
@keyframes fadeOutRightBig {
from {
    opacity: 1;
}
to {
    opacity: 0;
    transform: translate3d(2000px, 0, 0);
}
}
.animate__fadeOutRightBig {
  animation-name: fadeOutRightBig;
}
@keyframes fadeOutUp {
from {
    opacity: 1;
}
to {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
}
}
.animate__fadeOutUp {
  animation-name: fadeOutUp;
}
@keyframes fadeOutUpBig {
from {
    opacity: 1;
}
to {
    opacity: 0;
    transform: translate3d(0, -2000px, 0);
}
}
.animate__fadeOutUpBig {
  animation-name: fadeOutUpBig;
}
@keyframes fadeOutTopLeft {
from {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
to {
    opacity: 0;
    transform: translate3d(-100%, -100%, 0);
}
}
.animate__fadeOutTopLeft {
  animation-name: fadeOutTopLeft;
}
@keyframes fadeOutTopRight {
from {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
to {
    opacity: 0;
    transform: translate3d(100%, -100%, 0);
}
}
.animate__fadeOutTopRight {
  animation-name: fadeOutTopRight;
}
@keyframes fadeOutBottomRight {
from {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
to {
    opacity: 0;
    transform: translate3d(100%, 100%, 0);
}
}
.animate__fadeOutBottomRight {
  animation-name: fadeOutBottomRight;
}
@keyframes fadeOutBottomLeft {
from {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
to {
    opacity: 0;
    transform: translate3d(-100%, 100%, 0);
}
}
.animate__fadeOutBottomLeft {
  animation-name: fadeOutBottomLeft;
}
/* Flippers */
@keyframes flip {
from {
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0) rotate3d(0, 1, 0, -360deg);
    animation-timing-function: ease-out;
}
40% {
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px)
      rotate3d(0, 1, 0, -190deg);
    animation-timing-function: ease-out;
}
50% {
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px)
      rotate3d(0, 1, 0, -170deg);
    animation-timing-function: ease-in;
}
80% {
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95) translate3d(0, 0, 0)
      rotate3d(0, 1, 0, 0deg);
    animation-timing-function: ease-in;
}
to {
    transform: perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0) rotate3d(0, 1, 0, 0deg);
    animation-timing-function: ease-in;
}
}
.animate__animated.animate__flip {
  -webkit-backface-visibility: visible;
  backface-visibility: visible;
  animation-name: flip;
}
@keyframes flipInX {
from {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    animation-timing-function: ease-in;
    opacity: 0;
}
40% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    animation-timing-function: ease-in;
}
60% {
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
}
80% {
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
}
to {
    transform: perspective(400px);
}
}
.animate__flipInX {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  animation-name: flipInX;
}
@keyframes flipInY {
from {
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    animation-timing-function: ease-in;
    opacity: 0;
}
40% {
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    animation-timing-function: ease-in;
}
60% {
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1;
}
80% {
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
}
to {
    transform: perspective(400px);
}
}
.animate__flipInY {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  animation-name: flipInY;
}
@keyframes flipOutX {
from {
    transform: perspective(400px);
}
30% {
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
}
to {
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
}
}
.animate__flipOutX {
  animation-duration: calc(1s * 0.75);
  animation-duration: calc(var(--animate-duration) * 0.75);
  animation-name: flipOutX;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}
@keyframes flipOutY {
from {
    transform: perspective(400px);
}
30% {
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1;
}
to {
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0;
}
}
.animate__flipOutY {
  animation-duration: calc(1s * 0.75);
  animation-duration: calc(var(--animate-duration) * 0.75);
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  animation-name: flipOutY;
}
/* Lightspeed */
@keyframes lightSpeedInRight {
from {
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
}
60% {
    transform: skewX(20deg);
    opacity: 1;
}
80% {
    transform: skewX(-5deg);
}
to {
    transform: translate3d(0, 0, 0);
}
}
.animate__lightSpeedInRight {
  animation-name: lightSpeedInRight;
  animation-timing-function: ease-out;
}
@keyframes lightSpeedInLeft {
from {
    transform: translate3d(-100%, 0, 0) skewX(30deg);
    opacity: 0;
}
60% {
    transform: skewX(-20deg);
    opacity: 1;
}
80% {
    transform: skewX(5deg);
}
to {
    transform: translate3d(0, 0, 0);
}
}
.animate__lightSpeedInLeft {
  animation-name: lightSpeedInLeft;
  animation-timing-function: ease-out;
}
@keyframes lightSpeedOutRight {
from {
    opacity: 1;
}
to {
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
}
}
.animate__lightSpeedOutRight {
  animation-name: lightSpeedOutRight;
  animation-timing-function: ease-in;
}
@keyframes lightSpeedOutLeft {
from {
    opacity: 1;
}
to {
    transform: translate3d(-100%, 0, 0) skewX(-30deg);
    opacity: 0;
}
}
.animate__lightSpeedOutLeft {
  animation-name: lightSpeedOutLeft;
  animation-timing-function: ease-in;
}
/* Rotating entrances */
@keyframes rotateIn {
from {
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0;
}
to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
}
}
.animate__rotateIn {
  animation-name: rotateIn;
  transform-origin: center;
}
@keyframes rotateInDownLeft {
from {
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
}
to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
}
}
.animate__rotateInDownLeft {
  animation-name: rotateInDownLeft;
  transform-origin: left bottom;
}
@keyframes rotateInDownRight {
from {
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
}
to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
}
}
.animate__rotateInDownRight {
  animation-name: rotateInDownRight;
  transform-origin: right bottom;
}
@keyframes rotateInUpLeft {
from {
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
}
to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
}
}
.animate__rotateInUpLeft {
  animation-name: rotateInUpLeft;
  transform-origin: left bottom;
}
@keyframes rotateInUpRight {
from {
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0;
}
to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
}
}
.animate__rotateInUpRight {
  animation-name: rotateInUpRight;
  transform-origin: right bottom;
}
/* Rotating exits */
@keyframes rotateOut {
from {
    opacity: 1;
}
to {
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0;
}
}
.animate__rotateOut {
  animation-name: rotateOut;
  transform-origin: center;
}
@keyframes rotateOutDownLeft {
from {
    opacity: 1;
}
to {
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
}
}
.animate__rotateOutDownLeft {
  animation-name: rotateOutDownLeft;
  transform-origin: left bottom;
}
@keyframes rotateOutDownRight {
from {
    opacity: 1;
}
to {
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
}
}
.animate__rotateOutDownRight {
  animation-name: rotateOutDownRight;
  transform-origin: right bottom;
}
@keyframes rotateOutUpLeft {
from {
    opacity: 1;
}
to {
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
}
}
.animate__rotateOutUpLeft {
  animation-name: rotateOutUpLeft;
  transform-origin: left bottom;
}
@keyframes rotateOutUpRight {
from {
    opacity: 1;
}
to {
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0;
}
}
.animate__rotateOutUpRight {
  animation-name: rotateOutUpRight;
  transform-origin: right bottom;
}
/* Specials */
@keyframes hinge {
0% {
    animation-timing-function: ease-in-out;
}
20%,
  60% {
    transform: rotate3d(0, 0, 1, 80deg);
    animation-timing-function: ease-in-out;
}
40%,
  80% {
    transform: rotate3d(0, 0, 1, 60deg);
    animation-timing-function: ease-in-out;
    opacity: 1;
}
to {
    transform: translate3d(0, 700px, 0);
    opacity: 0;
}
}
.animate__hinge {
  animation-duration: calc(1s * 2);
  animation-duration: calc(var(--animate-duration) * 2);
  animation-name: hinge;
  transform-origin: top left;
}
@keyframes jackInTheBox {
from {
    opacity: 0;
    transform: scale(0.1) rotate(30deg);
    transform-origin: center bottom;
}
50% {
    transform: rotate(-10deg);
}
70% {
    transform: rotate(3deg);
}
to {
    opacity: 1;
    transform: scale(1);
}
}
.animate__jackInTheBox {
  animation-name: jackInTheBox;
}
/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@keyframes rollIn {
from {
    opacity: 0;
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
}
to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}
}
.animate__rollIn {
  animation-name: rollIn;
}
/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@keyframes rollOut {
from {
    opacity: 1;
}
to {
    opacity: 0;
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
}
}
.animate__rollOut {
  animation-name: rollOut;
}
/* Zooming entrances */
@keyframes zoomIn {
from {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
}
50% {
    opacity: 1;
}
}
.animate__zoomIn {
  animation-name: zoomIn;
}
@keyframes zoomInDown {
from {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}
60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
}
}
.animate__zoomInDown {
  animation-name: zoomInDown;
}
@keyframes zoomInLeft {
from {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}
60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
}
}
.animate__zoomInLeft {
  animation-name: zoomInLeft;
}
@keyframes zoomInRight {
from {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}
60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
}
}
.animate__zoomInRight {
  animation-name: zoomInRight;
}
@keyframes zoomInUp {
from {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}
60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
}
}
.animate__zoomInUp {
  animation-name: zoomInUp;
}
/* Zooming exits */
@keyframes zoomOut {
from {
    opacity: 1;
}
50% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
}
to {
    opacity: 0;
}
}
.animate__zoomOut {
  animation-name: zoomOut;
}
@keyframes zoomOutDown {
40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}
to {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
}
}
.animate__zoomOutDown {
  animation-name: zoomOutDown;
  transform-origin: center bottom;
}
@keyframes zoomOutLeft {
40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
}
to {
    opacity: 0;
    transform: scale(0.1) translate3d(-2000px, 0, 0);
}
}
.animate__zoomOutLeft {
  animation-name: zoomOutLeft;
  transform-origin: left center;
}
@keyframes zoomOutRight {
40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
}
to {
    opacity: 0;
    transform: scale(0.1) translate3d(2000px, 0, 0);
}
}
.animate__zoomOutRight {
  animation-name: zoomOutRight;
  transform-origin: right center;
}
@keyframes zoomOutUp {
40% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}
to {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
}
}
.animate__zoomOutUp {
  animation-name: zoomOutUp;
  transform-origin: center bottom;
}
/* Sliding entrances */
@keyframes slideInDown {
from {
    transform: translate3d(0, -100%, 0);
    visibility: visible;
}
to {
    transform: translate3d(0, 0, 0);
}
}
.animate__slideInDown {
  animation-name: slideInDown;
}
@keyframes slideInLeft {
from {
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
}
to {
    transform: translate3d(0, 0, 0);
}
}
.animate__slideInLeft {
  animation-name: slideInLeft;
}
@keyframes slideInRight {
from {
    transform: translate3d(100%, 0, 0);
    visibility: visible;
}
to {
    transform: translate3d(0, 0, 0);
}
}
.animate__slideInRight {
  animation-name: slideInRight;
}
@keyframes slideInUp {
from {
    transform: translate3d(0, 100%, 0);
    visibility: visible;
}
to {
    transform: translate3d(0, 0, 0);
}
}
.animate__slideInUp {
  animation-name: slideInUp;
}
/* Sliding exits */
@keyframes slideOutDown {
from {
    transform: translate3d(0, 0, 0);
}
to {
    visibility: hidden;
    transform: translate3d(0, 100%, 0);
}
}
.animate__slideOutDown {
  animation-name: slideOutDown;
}
@keyframes slideOutLeft {
from {
    transform: translate3d(0, 0, 0);
}
to {
    visibility: hidden;
    transform: translate3d(-100%, 0, 0);
}
}
.animate__slideOutLeft {
  animation-name: slideOutLeft;
}
@keyframes slideOutRight {
from {
    transform: translate3d(0, 0, 0);
}
to {
    visibility: hidden;
    transform: translate3d(100%, 0, 0);
}
}
.animate__slideOutRight {
  animation-name: slideOutRight;
}
@keyframes slideOutUp {
from {
    transform: translate3d(0, 0, 0);
}
to {
    visibility: hidden;
    transform: translate3d(0, -100%, 0);
}
}
.animate__slideOutUp {
  animation-name: slideOutUp;
}
@keyframes rotate {
0% {
		transform: rotate(0deg);
}
100% {
		transform: rotate(359deg) ;
}
}
.animate__rotate{
  animation-name: rotate;
  animation-timing-function: linear;
}
@font-face {
  font-family: "diygwui";
  /* Project id 2852989 */
  src: url("//at.alicdn.com/t/c/font_2852989_fa3xtm3h24.eot?t=1672039515796");
  /* IE9 */
  src: url("//at.alicdn.com/t/c/font_2852989_fa3xtm3h24.eot?t=1672039515796#iefix") format("embedded-opentype"), url("data:application/x-font-woff2;charset=utf-8;base64,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") format("woff2"), url("//at.alicdn.com/t/c/font_2852989_fa3xtm3h24.woff?t=1672039515796") format("woff"), url("//at.alicdn.com/t/c/font_2852989_fa3xtm3h24.ttf?t=1672039515796") format("truetype"), url("//at.alicdn.com/t/c/font_2852989_fa3xtm3h24.svg?t=1672039515796#diygwui") format("svg");
}
.diygwui {
  font-family: "diygwui" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.diy-icon-zuihouye:before {
  content: "\e601";
}
.diy-icon-diyiye:before {
  content: "\e602";
}
.diy-icon-weibiaoti545:before {
  content: "\e631";
}
.diy-icon-weibiaoti546:before {
  content: "\e632";
}
.diy-icon-rubber:before {
  content: "\ea15";
}
.diy-icon-alipay:before {
  content: "\e60e";
}
.diy-icon-android:before {
  content: "\e60f";
}
.diy-icon-apple:before {
  content: "\e610";
}
.diy-icon-baidu:before {
  content: "\e61e";
}
.diy-icon-countdown1:before {
  content: "\e638";
}
.diy-icon-creative1:before {
  content: "\e639";
}
.diy-icon-wechat:before {
  content: "\e76b";
}
.diy-icon-shouru:before {
  content: "\e76e";
}
.diy-icon-youhuiquan:before {
  content: "\e770";
}
.diy-icon-QQ:before {
  content: "\e771";
}
.diy-icon-anquan:before {
  content: "\e772";
}
.diy-icon-erweima:before {
  content: "\e773";
}
.diy-icon-hongbao:before {
  content: "\e774";
}
.diy-icon-fenxiang:before {
  content: "\e775";
}
.diy-icon-jingshi:before {
  content: "\e776";
}
.diy-icon-lihe:before {
  content: "\e777";
}
.diy-icon-mingci:before {
  content: "\e778";
}
.diy-icon-shouru2:before {
  content: "\e779";
}
.diy-icon-tixing:before {
  content: "\e77a";
}
.diy-icon-wendang:before {
  content: "\e77b";
}
.diy-icon-tupian:before {
  content: "\e77e";
}
.diy-icon-xianjinquan:before {
  content: "\e77f";
}
.diy-icon-xiangji:before {
  content: "\e780";
}
.diy-icon-youxi:before {
  content: "\e781";
}
.diy-icon-yuyin:before {
  content: "\e782";
}
.diy-icon-star:before {
  content: "\e7df";
}
.diy-icon-starfill:before {
  content: "\e86a";
}
.diy-icon-writefill:before {
  content: "\e600";
}
.diy-icon-jiaonang:before {
  content: "\e76d";
}
.diy-icon-shop:before {
  content: "\e736";
}
.diy-icon-shopfill:before {
  content: "\e737";
}
.diy-icon-pullright:before {
  content: "\e738";
}
.diy-icon-settings:before {
  content: "\e739";
}
.diy-icon-similar:before {
  content: "\e73a";
}
.diy-icon-sort:before {
  content: "\e73b";
}
.diy-icon-sound:before {
  content: "\e73c";
}
.diy-icon-skinfill:before {
  content: "\e73d";
}
.diy-icon-skin:before {
  content: "\e73e";
}
.diy-icon-stop:before {
  content: "\e73f";
}
.diy-icon-squarecheckfill:before {
  content: "\e740";
}
.diy-icon-square:before {
  content: "\e741";
}
.diy-icon-tag:before {
  content: "\e742";
}
.diy-icon-add:before {
  content: "\e643";
}
.diy-icon-subscription:before {
  content: "\e743";
}
.diy-icon-activityfill:before {
  content: "\e644";
}
.diy-icon-squarecheck:before {
  content: "\e744";
}
.diy-icon-addressbook:before {
  content: "\e645";
}
.diy-icon-taoxiaopu:before {
  content: "\e745";
}
.diy-icon-album:before {
  content: "\e646";
}
.diy-icon-taxi:before {
  content: "\e746";
}
.diy-icon-all:before {
  content: "\e647";
}
.diy-icon-text:before {
  content: "\e747";
}
.diy-icon-appreciate:before {
  content: "\e648";
}
.diy-icon-sponsorfill:before {
  content: "\e748";
}
.diy-icon-activity:before {
  content: "\e649";
}
.diy-icon-time:before {
  content: "\e749";
}
.diy-icon-attentionfavor:before {
  content: "\e64a";
}
.diy-icon-tagfill:before {
  content: "\e74a";
}
.diy-icon-attention:before {
  content: "\e64b";
}
.diy-icon-timefill:before {
  content: "\e74b";
}
.diy-icon-apps:before {
  content: "\e64c";
}
.diy-icon-title:before {
  content: "\e74c";
}
.diy-icon-attentionfill:before {
  content: "\e64d";
}
.diy-icon-ticket:before {
  content: "\e74d";
}
.diy-icon-attentionfavorfill:before {
  content: "\e64e";
}
.diy-icon-triangledownfill:before {
  content: "\e74e";
}
.diy-icon-attentionforbidfill:before {
  content: "\e64f";
}
.diy-icon-triangleupfill:before {
  content: "\e74f";
}
.diy-icon-baby:before {
  content: "\e650";
}
.diy-icon-top:before {
  content: "\e750";
}
.diy-icon-backdelete:before {
  content: "\e651";
}
.diy-icon-unlock:before {
  content: "\e751";
}
.diy-icon-back_android:before {
  content: "\e652";
}
.diy-icon-upblock:before {
  content: "\e752";
}
.diy-icon-babyfill:before {
  content: "\e653";
}
.diy-icon-upload:before {
  content: "\e753";
}
.diy-icon-back:before {
  content: "\e654";
}
.diy-icon-titles:before {
  content: "\e754";
}
.diy-icon-brand:before {
  content: "\e655";
}
.diy-icon-unfold:before {
  content: "\e755";
}
.diy-icon-barcode:before {
  content: "\e656";
}
.diy-icon-usefull:before {
  content: "\e756";
}
.diy-icon-brandfill:before {
  content: "\e657";
}
.diy-icon-upstagefill:before {
  content: "\e757";
}
.diy-icon-backwardfill:before {
  content: "\e658";
}
.diy-icon-upstage:before {
  content: "\e758";
}
.diy-icon-btn:before {
  content: "\e659";
}
.diy-icon-vip:before {
  content: "\e759";
}
.diy-icon-cai:before {
  content: "\e65a";
}
.diy-icon-video:before {
  content: "\e75a";
}
.diy-icon-calendar:before {
  content: "\e65b";
}
.diy-icon-videofill:before {
  content: "\e75b";
}
.diy-icon-camerafill1:before {
  content: "\e65c";
}
.diy-icon-vipcard:before {
  content: "\e75c";
}
.diy-icon-bad:before {
  content: "\e65d";
}
.diy-icon-voice:before {
  content: "\e75d";
}
.diy-icon-cameraaddfill:before {
  content: "\e65e";
}
.diy-icon-warn:before {
  content: "\e75e";
}
.diy-icon-camera:before {
  content: "\e65f";
}
.diy-icon-warnfill:before {
  content: "\e75f";
}
.diy-icon-appreciatefill:before {
  content: "\e660";
}
.diy-icon-voicefill:before {
  content: "\e760";
}
.diy-icon-camerarotate:before {
  content: "\e661";
}
.diy-icon-weibo:before {
  content: "\e761";
}
.diy-icon-card:before {
  content: "\e662";
}
.diy-icon-usefullfill:before {
  content: "\e762";
}
.diy-icon-camerafill:before {
  content: "\e663";
}
.diy-icon-wefill:before {
  content: "\e763";
}
.diy-icon-attentionforbid:before {
  content: "\e664";
}
.diy-icon-weixin:before {
  content: "\e764";
}
.diy-icon-cardboard:before {
  content: "\e665";
}
.diy-icon-we:before {
  content: "\e765";
}
.diy-icon-cardboardfill:before {
  content: "\e666";
}
.diy-icon-wenzi:before {
  content: "\e766";
}
.diy-icon-cardboardforbid:before {
  content: "\e667";
}
.diy-icon-weblock:before {
  content: "\e767";
}
.diy-icon-choicenessfill:before {
  content: "\e668";
}
.diy-icon-wifi:before {
  content: "\e768";
}
.diy-icon-choiceness:before {
  content: "\e669";
}
.diy-icon-weunblock:before {
  content: "\e769";
}
.diy-icon-cartfill:before {
  content: "\e66a";
}
.diy-icon-write:before {
  content: "\e76a";
}
.diy-icon-cart:before {
  content: "\e66b";
}
.diy-icon-check:before {
  content: "\e66c";
}
.diy-icon-sponsor:before {
  content: "\e76c";
}
.diy-icon-cascades:before {
  content: "\e66d";
}
.diy-icon-cameraadd:before {
  content: "\e66e";
}
.diy-icon-circle:before {
  content: "\e66f";
}
.diy-icon-circlefill:before {
  content: "\e670";
}
.diy-icon-clothes:before {
  content: "\e671";
}
.diy-icon-colorlens:before {
  content: "\e672";
}
.diy-icon-clothesfill:before {
  content: "\e673";
}
.diy-icon-close:before {
  content: "\e674";
}
.diy-icon-commandfill:before {
  content: "\e675";
}
.diy-icon-comment:before {
  content: "\e676";
}
.diy-icon-command:before {
  content: "\e677";
}
.diy-icon-coin:before {
  content: "\e678";
}
.diy-icon-commentfill:before {
  content: "\e679";
}
.diy-icon-countdownfill:before {
  content: "\e67a";
}
.diy-icon-communityfill:before {
  content: "\e67b";
}
.diy-icon-community:before {
  content: "\e67c";
}
.diy-icon-copy:before {
  content: "\e67d";
}
.diy-icon-countdown:before {
  content: "\e67e";
}
.diy-icon-deletefill:before {
  content: "\e67f";
}
.diy-icon-creativefill:before {
  content: "\e680";
}
.diy-icon-crown:before {
  content: "\e681";
}
.diy-icon-crownfill:before {
  content: "\e682";
}
.diy-icon-deliver:before {
  content: "\e683";
}
.diy-icon-delete:before {
  content: "\e684";
}
.diy-icon-deliver_fill:before {
  content: "\e685";
}
.diy-icon-creative:before {
  content: "\e686";
}
.diy-icon-cut:before {
  content: "\e687";
}
.diy-icon-dianhua:before {
  content: "\e688";
}
.diy-icon-discoverfill:before {
  content: "\e689";
}
.diy-icon-discover:before {
  content: "\e68a";
}
.diy-icon-edit:before {
  content: "\e68b";
}
.diy-icon-emoji:before {
  content: "\e68c";
}
.diy-icon-emojifill:before {
  content: "\e68d";
}
.diy-icon-ellipse:before {
  content: "\e68e";
}
.diy-icon-down:before {
  content: "\e68f";
}
.diy-icon-evaluate_fill:before {
  content: "\e690";
}
.diy-icon-emojiflashfill:before {
  content: "\e691";
}
.diy-icon-explore:before {
  content: "\e692";
}
.diy-icon-exit:before {
  content: "\e693";
}
.diy-icon-female:before {
  content: "\e694";
}
.diy-icon-favorfill:before {
  content: "\e695";
}
.diy-icon-evaluate:before {
  content: "\e696";
}
.diy-icon-explorefill:before {
  content: "\e697";
}
.diy-icon-expressman:before {
  content: "\e698";
}
.diy-icon-flashlightclose:before {
  content: "\e699";
}
.diy-icon-file:before {
  content: "\e69a";
}
.diy-icon-flashbuyfill:before {
  content: "\e69b";
}
.diy-icon-filter:before {
  content: "\e69c";
}
.diy-icon-favor:before {
  content: "\e69d";
}
.diy-icon-flashlightopen:before {
  content: "\e69e";
}
.diy-icon-fold:before {
  content: "\e69f";
}
.diy-icon-font:before {
  content: "\e6a0";
}
.diy-icon-focus:before {
  content: "\e6a1";
}
.diy-icon-fork:before {
  content: "\e6a2";
}
.diy-icon-footprint:before {
  content: "\e6a3";
}
.diy-icon-forwardfill:before {
  content: "\e6a4";
}
.diy-icon-forward:before {
  content: "\e6a5";
}
.diy-icon-formfill:before {
  content: "\e6a6";
}
.diy-icon-friendadd:before {
  content: "\e6a7";
}
.diy-icon-friendaddfill:before {
  content: "\e6a8";
}
.diy-icon-friendfamous:before {
  content: "\e6a9";
}
.diy-icon-friendfill:before {
  content: "\e6aa";
}
.diy-icon-friend:before {
  content: "\e6ab";
}
.diy-icon-full:before {
  content: "\e6ac";
}
.diy-icon-form:before {
  content: "\e6ad";
}
.diy-icon-github:before {
  content: "\e6ae";
}
.diy-icon-goods:before {
  content: "\e6af";
}
.diy-icon-global:before {
  content: "\e6b0";
}
.diy-icon-goodsnewfill:before {
  content: "\e6b1";
}
.diy-icon-goodsnew:before {
  content: "\e6b2";
}
.diy-icon-game:before {
  content: "\e6b3";
}
.diy-icon-goodsfill:before {
  content: "\e6b4";
}
.diy-icon-group:before {
  content: "\e6b5";
}
.diy-icon-group_fill:before {
  content: "\e6b6";
}
.diy-icon-homefill:before {
  content: "\e6b7";
}
.diy-icon-goodsfavor:before {
  content: "\e6b8";
}
.diy-icon-home:before {
  content: "\e6b9";
}
.diy-icon-hot:before {
  content: "\e6ba";
}
.diy-icon-friendfavor:before {
  content: "\e6bb";
}
.diy-icon-icon:before {
  content: "\e6bc";
}
.diy-icon-hotfill:before {
  content: "\e6bd";
}
.diy-icon-icloading:before {
  content: "\e6be";
}
.diy-icon-keyboard:before {
  content: "\e6bf";
}
.diy-icon-light:before {
  content: "\e6c0";
}
.diy-icon-info:before {
  content: "\e6c1";
}
.diy-icon-infofill:before {
  content: "\e6c2";
}
.diy-icon-lightfill:before {
  content: "\e6c3";
}
.diy-icon-lightforbid:before {
  content: "\e6c4";
}
.diy-icon-like:before {
  content: "\e6c5";
}
.diy-icon-lightauto:before {
  content: "\e6c6";
}
.diy-icon-list:before {
  content: "\e6c7";
}
.diy-icon-loading1:before {
  content: "\e6c8";
}
.diy-icon-likefill:before {
  content: "\e6c9";
}
.diy-icon-loading:before {
  content: "\e6ca";
}
.diy-icon-link:before {
  content: "\e6cb";
}
.diy-icon-location:before {
  content: "\e6cc";
}
.diy-icon-locationfill:before {
  content: "\e6cd";
}
.diy-icon-magic:before {
  content: "\e6ce";
}
.diy-icon-lock:before {
  content: "\e6cf";
}
.diy-icon-mail:before {
  content: "\e6d0";
}
.diy-icon-mark:before {
  content: "\e6d1";
}
.diy-icon-medalfill:before {
  content: "\e6d2";
}
.diy-icon-markfill:before {
  content: "\e6d3";
}
.diy-icon-medal:before {
  content: "\e6d4";
}
.diy-icon-mobile:before {
  content: "\e6d5";
}
.diy-icon-male:before {
  content: "\e6d6";
}
.diy-icon-mobilefill:before {
  content: "\e6d7";
}
.diy-icon-more:before {
  content: "\e6d8";
}
.diy-icon-message:before {
  content: "\e6d9";
}
.diy-icon-loading2:before {
  content: "\e6da";
}
.diy-icon-move:before {
  content: "\e6db";
}
.diy-icon-musicfill:before {
  content: "\e6dc";
}
.diy-icon-musicforbidfill:before {
  content: "\e6dd";
}
.diy-icon-messagefill:before {
  content: "\e6de";
}
.diy-icon-moneybagfill:before {
  content: "\e6df";
}
.diy-icon-moreandroid:before {
  content: "\e6e0";
}
.diy-icon-moneybag:before {
  content: "\e6e1";
}
.diy-icon-news:before {
  content: "\e6e2";
}
.diy-icon-newfill:before {
  content: "\e6e3";
}
.diy-icon-my:before {
  content: "\e6e4";
}
.diy-icon-new:before {
  content: "\e6e5";
}
.diy-icon-myfill:before {
  content: "\e6e6";
}
.diy-icon-newshotfill:before {
  content: "\e6e7";
}
.diy-icon-newshot:before {
  content: "\e6e8";
}
.diy-icon-notice_forbid_fill:before {
  content: "\e6e9";
}
.diy-icon-newsfill:before {
  content: "\e6ea";
}
.diy-icon-notificationfill:before {
  content: "\e6eb";
}
.diy-icon-notice:before {
  content: "\e6ec";
}
.diy-icon-notification:before {
  content: "\e6ed";
}
.diy-icon-noticefill:before {
  content: "\e6ee";
}
.diy-icon-order:before {
  content: "\e6ef";
}
.diy-icon-paintfill:before {
  content: "\e6f0";
}
.diy-icon-notificationforbidfill:before {
  content: "\e6f1";
}
.diy-icon-peoplefill:before {
  content: "\e6f2";
}
.diy-icon-paint:before {
  content: "\e6f3";
}
.diy-icon-peoplelist:before {
  content: "\e6f4";
}
.diy-icon-pay:before {
  content: "\e6f5";
}
.diy-icon-phone:before {
  content: "\e6f6";
}
.diy-icon-picfill:before {
  content: "\e6f7";
}
.diy-icon-pick:before {
  content: "\e6f8";
}
.diy-icon-pic:before {
  content: "\e6f9";
}
.diy-icon-post:before {
  content: "\e6fa";
}
.diy-icon-play_forward_fill:before {
  content: "\e6fb";
}
.diy-icon-present:before {
  content: "\e6fc";
}
.diy-icon-people:before {
  content: "\e6fd";
}
.diy-icon-playfill:before {
  content: "\e6fe";
}
.diy-icon-profile:before {
  content: "\e6ff";
}
.diy-icon-profilefill:before {
  content: "\e700";
}
.diy-icon-pulldown:before {
  content: "\e701";
}
.diy-icon-presentfill:before {
  content: "\e702";
}
.diy-icon-pullleft:before {
  content: "\e703";
}
.diy-icon-pullup:before {
  content: "\e704";
}
.diy-icon-punch:before {
  content: "\e705";
}
.diy-icon-questionfill:before {
  content: "\e706";
}
.diy-icon-radiobox:before {
  content: "\e707";
}
.diy-icon-question:before {
  content: "\e708";
}
.diy-icon-rank:before {
  content: "\e709";
}
.diy-icon-qrcode:before {
  content: "\e70a";
}
.diy-icon-rankfill:before {
  content: "\e70b";
}
.diy-icon-radioboxfill:before {
  content: "\e70c";
}
.diy-icon-qr_code:before {
  content: "\e70d";
}
.diy-icon-recordfill:before {
  content: "\e70e";
}
.diy-icon-record:before {
  content: "\e70f";
}
.diy-icon-read:before {
  content: "\e710";
}
.diy-icon-recharge:before {
  content: "\e711";
}
.diy-icon-refresh:before {
  content: "\e712";
}
.diy-icon-redpacket:before {
  content: "\e713";
}
.diy-icon-rechargefill:before {
  content: "\e714";
}
.diy-icon-redpacket_fill:before {
  content: "\e715";
}
.diy-icon-refresharrow:before {
  content: "\e716";
}
.diy-icon-refund:before {
  content: "\e717";
}
.diy-icon-round:before {
  content: "\e718";
}
.diy-icon-repeal:before {
  content: "\e719";
}
.diy-icon-right:before {
  content: "\e71a";
}
.diy-icon-repair:before {
  content: "\e71b";
}
.diy-icon-remind:before {
  content: "\e71c";
}
.diy-icon-repairfill:before {
  content: "\e71d";
}
.diy-icon-roundadd:before {
  content: "\e71e";
}
.diy-icon-roundaddfill:before {
  content: "\e71f";
}
.diy-icon-roundcheckfill:before {
  content: "\e720";
}
.diy-icon-rounddown:before {
  content: "\e721";
}
.diy-icon-roundclose:before {
  content: "\e722";
}
.diy-icon-roundleftfill-copy:before {
  content: "\e723";
}
.diy-icon-roundcheck:before {
  content: "\e724";
}
.diy-icon-roundright:before {
  content: "\e725";
}
.diy-icon-safe:before {
  content: "\e726";
}
.diy-icon-roundrightfill:before {
  content: "\e727";
}
.diy-icon-roundclosefill:before {
  content: "\e728";
}
.diy-icon-same:before {
  content: "\e729";
}
.diy-icon-samefill:before {
  content: "\e72a";
}
.diy-icon-searchlist:before {
  content: "\e72b";
}
.diy-icon-search:before {
  content: "\e72c";
}
.diy-icon-scan:before {
  content: "\e72d";
}
.diy-icon-send:before {
  content: "\e72e";
}
.diy-icon-selectionfill:before {
  content: "\e72f";
}
.diy-icon-selection:before {
  content: "\e730";
}
.diy-icon-settingsfill:before {
  content: "\e731";
}
.diy-icon-service:before {
  content: "\e732";
}
.diy-icon-shake:before {
  content: "\e733";
}
.diy-icon-servicefill:before {
  content: "\e734";
}
.diy-icon-share:before {
  content: "\e735";
}
body, page {
  --red: #e54d42;
  --orange: #f37b1d;
  --yellow: #fbbd08;
  --olive: #8dc63f;
  --green: #07c160;
  --cyan: #1cbbb4;
  --blue: #0081ff;
  --purple: #6739b6;
  --mauve: #9c26b0;
  --pink: #e03997;
  --brown: #a5673f;
  --grey: #8799a3;
  --black: #333333;
  --darkGray: #666666;
  --gray: #aaaaaa;
  --ghostWhite: #f1f1f1;
  --white: #ffffff;
  /* 浅色 */
  --redLight: #fadbd9;
  --orangeLight: #fde6d2;
  --yellowLight: #fef2ce;
  --oliveLight: #e8f4d9;
  --greenLight: #d7f0db;
  --cyanLight: #d2f1f0;
  --blueLight: #cce6ff;
  --purpleLight: #e1d7f0;
  --mauveLight: #ebd4ef;
  --pinkLight: #f9d7ea;
  --brownLight: #ede1d9;
  --greyLight: #e7ebed;
  /* 渐变色 */
  --redGradual: linear-gradient(45deg, #f43f3b, #ec008c);
  --orangeGradual: linear-gradient(45deg, #ff9700, #ed1c24);
  --greenGradual: linear-gradient(45deg, #39b54a, #8dc63f);
  --purpleGradual: linear-gradient(45deg, #9000ff, #5e00ff);
  --pinkGradual: linear-gradient(45deg, #ec008c, #6739b6);
  --blueGradual: linear-gradient(45deg, #0081ff, #1cbbb4);
  /* 阴影透明色 */
  --ShadowSize: 0px 0px 8rpx;
  --whiteShadow: rgba(0, 0, 0, 0.15);
  --redShadow: rgba(204, 69, 59, 0.2);
  --orangeShadow: rgba(217, 109, 26, 0.2);
  --yellowShadow: rgba(224, 170, 7, 0.2);
  --oliveShadow: rgba(124, 173, 55, 0.2);
  --greenShadow: rgba(48, 156, 63, 0.3);
  --cyanShadow: rgba(28, 187, 180, 0.2);
  --blueShadow: rgba(0, 102, 204, 0.2);
  --purpleShadow: rgba(88, 48, 156, 0.2);
  --mauveShadow: rgba(133, 33, 150, 0.2);
  --pinkShadow: rgba(199, 50, 134, 0.2);
  --brownShadow: rgba(140, 88, 53, 0.2);
  --greyShadow: rgba(114, 130, 138, 0.2);
  --grayShadow: rgba(114, 130, 138, 0.2);
  --blackShadow: rgba(26, 26, 26, 0.16);
  --primary-font-size:24rpx;
  --form-label-width:5em;
  font-size: 24rpx;
}
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 6px;
}
::-webkit-scrollbar-corner {
  display: none;
}
[class*=diygw-] {
  font-size: inherit;
}
view,
scroll-view,
swiper,
button,
input,
textarea,
label,
navigator,
image {
  box-sizing: border-box;
}
.htmlcontent {
  white-space: pre-line;
  width: 100%;
}
.clearfix {
  box-sizing: border-box;
  clear: both;
}
.clearfix::before, .clearfix::after {
  clear: both;
  display: table;
}
.scroll-view ::-webkit-scrollbar, .uni-scroll-view::-webkit-scrollbar, .uni-scroll-view-content::-webkit-scrollbar, .scroll-view::-webkit-scrollbar, scroll-view::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}
.hidden {
  display: none !important;
}
.pointer {
  cursor: pointer;
}
.container {
  background-size: cover;
  background-position: top center;
  min-height: 100vh;
}
.placeholder {
  color: #c0c4cc;
}
.diygw-bg {
  position: relative;
}
.diygw-bg::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  z-index: -1;
  bottom: 0;
  opacity: 1;
  transform: scale(1, 1);
  background-size: 100% 100%;
  background-image: var(--diygw-image);
}
.diygw-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.response {
  width: 100%;
}
.response > view {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
}
.diygw-autoview {
  position: relative;
}
.diygw-safe-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.uni-tabbar-bottom, .uni-page-head, .uni-picker-container {
  z-index: 999999;
}
.uni-sample-toast, uni-toast, uni-modal, .uni-modal {
  z-index: 9999999 !important;
}
.uni-picker-container {
  z-index: 9999999 !important;
}
.diygw-autoview {
  position: relative;
  overflow: hidden;
}
.diygw-dropdown {
  overflow: inherit !important;
}
.diygw-col-24 uni-video {
  width: 100% !important;
}
.diygw-absolute, .diygw-absolute-bottom {
  position: absolute !important;
  z-index: 999;
}
.diygw-absolute-bottom {
  top: inherit !important;
  bottom: 0;
}
.diygw-top {
  position: fixed !important;
  z-index: 999999;
  left: 0px;
  top: 0px;
  width: 100%;
}
.not-border {
  border: 0 !important;
}
.not-border::after, .not-border::before {
  border: 0 !important;
}
.not-border.diygw-tag {
  padding-left: 0;
}
.font-normal {
  font-weight: normal;
}
.font-bold {
  font-weight: bold;
}
.border {
  border: 0;
}
.width-auto {
  width: auto !important;
}
[class*=diy-icon-] {
  font-family: "diygwui" !important;
  font-size: 32rpx;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.solid,
.dashed {
  position: relative;
}
.solid::after,
.dashed::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
}
.solid::after,
.dashed::after {
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  transform: scale(0.5);
}
.solid-top,
.dashed-top {
  position: relative;
}
.solid-top::after,
.dashed-top::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
}
.solid-top::after,
.dashed-top::after {
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  transform: scale(0.5);
}
.solid-right,
.dashed-right {
  position: relative;
}
.solid-right::after,
.dashed-right::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
}
.solid-right::after,
.dashed-right::after {
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
  transform: scale(0.5);
}
.solid-bottom,
.dashed-bottom {
  position: relative;
}
.solid-bottom::after,
.dashed-bottom::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
}
.solid-bottom::after,
.dashed-bottom::after {
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  transform: scale(0.5);
}
.solid-left,
.dashed-left {
  position: relative;
}
.solid-left::after,
.dashed-left::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
}
.solid-left::after,
.dashed-left::after {
  border-left: 1rpx solid rgba(0, 0, 0, 0.1);
  transform: scale(0.5);
}
.margin {
  margin: 20rpx !important;
}
.margin-top {
  margin-top: 20rpx !important;
}
.margin-right {
  margin-right: 20rpx !important;
}
.margin-bottom {
  margin-bottom: 20rpx !important;
}
.margin-left {
  margin-left: 20rpx !important;
}
.padding {
  padding: 20rpx;
}
.padding-top {
  padding: 20rpx;
}
.padding-right {
  padding: 20rpx;
}
.padding-bottom {
  padding: 20rpx;
}
.padding-left {
  padding: 20rpx;
}
.margin-xs {
  margin: 10rpx !important;
}
.margin-lr-xs {
  margin-left: 10rpx !important;
  margin-right: 10rpx !important;
}
.margin-top-xs {
  margin-top: 10rpx !important;
}
.margin-right-xs {
  margin-right: 10rpx !important;
}
.margin-bottom-xs {
  margin-bottom: 10rpx !important;
}
.margin-left-xs {
  margin-left: 10rpx !important;
}
.padding-xs {
  padding: 10rpx !important;
}
.padding-lr-xs {
  padding-left: 10rpx !important;
  padding-right: 10rpx !important;
}
.padding-top-xs {
  padding-top: 10rpx;
}
.padding-right-xs {
  padding-right: 10rpx;
}
.padding-bottom-xs {
  padding-bottom: 10rpx;
}
.padding-left-xs {
  padding-left: 10rpx;
}
.margin-sm {
  margin: 20rpx !important;
}
.margin-lr-sm {
  margin-left: 20rpx !important;
  margin-right: 20rpx !important;
}
.margin-top-sm {
  margin-top: 20rpx !important;
}
.margin-right-sm {
  margin-right: 20rpx !important;
}
.margin-bottom-sm {
  margin-bottom: 20rpx !important;
}
.margin-left-sm {
  margin-left: 20rpx !important;
}
.padding-sm {
  padding: 20rpx !important;
}
.padding-lr-sm {
  padding-left: 20rpx !important;
  padding-right: 20rpx !important;
}
.padding-top-sm {
  padding-top: 20rpx;
}
.padding-right-sm {
  padding-right: 20rpx;
}
.padding-bottom-sm {
  padding-bottom: 20rpx;
}
.padding-left-sm {
  padding-left: 20rpx;
}
.margin-md {
  margin: 24rpx !important;
}
.margin-lr-md {
  margin-left: 24rpx !important;
  margin-right: 24rpx !important;
}
.margin-top-md {
  margin-top: 24rpx !important;
}
.margin-right-md {
  margin-right: 24rpx !important;
}
.margin-bottom-md {
  margin-bottom: 24rpx !important;
}
.margin-left-md {
  margin-left: 24rpx !important;
}
.padding-md {
  padding: 24rpx !important;
}
.padding-lr-md {
  padding-left: 24rpx !important;
  padding-right: 24rpx !important;
}
.padding-top-md {
  padding-top: 24rpx;
}
.padding-right-md {
  padding-right: 24rpx;
}
.padding-bottom-md {
  padding-bottom: 24rpx;
}
.padding-left-md {
  padding-left: 24rpx;
}
.margin-lg {
  margin: 40rpx !important;
}
.margin-lr-lg {
  margin-left: 40rpx !important;
  margin-right: 40rpx !important;
}
.margin-top-lg {
  margin-top: 40rpx !important;
}
.margin-right-lg {
  margin-right: 40rpx !important;
}
.margin-bottom-lg {
  margin-bottom: 40rpx !important;
}
.margin-left-lg {
  margin-left: 40rpx !important;
}
.padding-lg {
  padding: 40rpx !important;
}
.padding-lr-lg {
  padding-left: 40rpx !important;
  padding-right: 40rpx !important;
}
.padding-top-lg {
  padding-top: 40rpx;
}
.padding-right-lg {
  padding-right: 40rpx;
}
.padding-bottom-lg {
  padding-bottom: 40rpx;
}
.padding-left-lg {
  padding-left: 40rpx;
}
.margin-xl {
  margin: 60rpx !important;
}
.margin-lr-xl {
  margin-left: 60rpx !important;
  margin-right: 60rpx !important;
}
.margin-top-xl {
  margin-top: 60rpx !important;
}
.margin-right-xl {
  margin-right: 60rpx !important;
}
.margin-bottom-xl {
  margin-bottom: 60rpx !important;
}
.margin-left-xl {
  margin-left: 60rpx !important;
}
.padding-xl {
  padding: 60rpx !important;
}
.padding-lr-xl {
  padding-left: 60rpx !important;
  padding-right: 60rpx !important;
}
.padding-top-xl {
  padding-top: 60rpx;
}
.padding-right-xl {
  padding-right: 60rpx;
}
.padding-bottom-xl {
  padding-bottom: 60rpx;
}
.padding-left-xl {
  padding-left: 60rpx;
}
[class*=diygw-col-] {
  float: left;
  box-sizing: border-box;
  position: relative;
}
.diygw-col-0 {
  width: auto;
}
.diygw-col-1 {
  width: 4.1666666667% !important;
}
.diygw-col-offset-1 {
  margin-left: 4.1666666667%;
}
.diygw-col-pull-1 {
  position: relative;
  right: 4.1666666667%;
}
.diygw-col-push-1 {
  position: relative;
  left: 4.1666666667%;
}
.diygw-col-2 {
  width: 8.3333333333% !important;
}
.diygw-col-offset-2 {
  margin-left: 8.3333333333%;
}
.diygw-col-pull-2 {
  position: relative;
  right: 8.3333333333%;
}
.diygw-col-push-2 {
  position: relative;
  left: 8.3333333333%;
}
.diygw-col-3 {
  width: 12.5% !important;
}
.diygw-col-offset-3 {
  margin-left: 12.5%;
}
.diygw-col-pull-3 {
  position: relative;
  right: 12.5%;
}
.diygw-col-push-3 {
  position: relative;
  left: 12.5%;
}
.diygw-col-4 {
  width: 16.6666666667% !important;
}
.diygw-col-offset-4 {
  margin-left: 16.6666666667%;
}
.diygw-col-pull-4 {
  position: relative;
  right: 16.6666666667%;
}
.diygw-col-push-4 {
  position: relative;
  left: 16.6666666667%;
}
.diygw-col-5 {
  width: 20.8333333333% !important;
}
.diygw-col-offset-5 {
  margin-left: 20.8333333333%;
}
.diygw-col-pull-5 {
  position: relative;
  right: 20.8333333333%;
}
.diygw-col-push-5 {
  position: relative;
  left: 20.8333333333%;
}
.diygw-col-6 {
  width: 25% !important;
}
.diygw-col-offset-6 {
  margin-left: 25%;
}
.diygw-col-pull-6 {
  position: relative;
  right: 25%;
}
.diygw-col-push-6 {
  position: relative;
  left: 25%;
}
.diygw-col-7 {
  width: 29.1666666667% !important;
}
.diygw-col-offset-7 {
  margin-left: 29.1666666667%;
}
.diygw-col-pull-7 {
  position: relative;
  right: 29.1666666667%;
}
.diygw-col-push-7 {
  position: relative;
  left: 29.1666666667%;
}
.diygw-col-8 {
  width: 33.3333333333% !important;
}
.diygw-col-offset-8 {
  margin-left: 33.3333333333%;
}
.diygw-col-pull-8 {
  position: relative;
  right: 33.3333333333%;
}
.diygw-col-push-8 {
  position: relative;
  left: 33.3333333333%;
}
.diygw-col-9 {
  width: 37.5% !important;
}
.diygw-col-offset-9 {
  margin-left: 37.5%;
}
.diygw-col-pull-9 {
  position: relative;
  right: 37.5%;
}
.diygw-col-push-9 {
  position: relative;
  left: 37.5%;
}
.diygw-col-10 {
  width: 41.6666666667% !important;
}
.diygw-col-offset-10 {
  margin-left: 41.6666666667%;
}
.diygw-col-pull-10 {
  position: relative;
  right: 41.6666666667%;
}
.diygw-col-push-10 {
  position: relative;
  left: 41.6666666667%;
}
.diygw-col-11 {
  width: 45.8333333333% !important;
}
.diygw-col-offset-11 {
  margin-left: 45.8333333333%;
}
.diygw-col-pull-11 {
  position: relative;
  right: 45.8333333333%;
}
.diygw-col-push-11 {
  position: relative;
  left: 45.8333333333%;
}
.diygw-col-12 {
  width: 50% !important;
}
.diygw-col-offset-12 {
  margin-left: 50%;
}
.diygw-col-pull-12 {
  position: relative;
  right: 50%;
}
.diygw-col-push-12 {
  position: relative;
  left: 50%;
}
.diygw-col-13 {
  width: 54.1666666667% !important;
}
.diygw-col-offset-13 {
  margin-left: 54.1666666667%;
}
.diygw-col-pull-13 {
  position: relative;
  right: 54.1666666667%;
}
.diygw-col-push-13 {
  position: relative;
  left: 54.1666666667%;
}
.diygw-col-14 {
  width: 58.3333333333% !important;
}
.diygw-col-offset-14 {
  margin-left: 58.3333333333%;
}
.diygw-col-pull-14 {
  position: relative;
  right: 58.3333333333%;
}
.diygw-col-push-14 {
  position: relative;
  left: 58.3333333333%;
}
.diygw-col-15 {
  width: 62.5% !important;
}
.diygw-col-offset-15 {
  margin-left: 62.5%;
}
.diygw-col-pull-15 {
  position: relative;
  right: 62.5%;
}
.diygw-col-push-15 {
  position: relative;
  left: 62.5%;
}
.diygw-col-16 {
  width: 66.6666666667% !important;
}
.diygw-col-offset-16 {
  margin-left: 66.6666666667%;
}
.diygw-col-pull-16 {
  position: relative;
  right: 66.6666666667%;
}
.diygw-col-push-16 {
  position: relative;
  left: 66.6666666667%;
}
.diygw-col-17 {
  width: 70.8333333333% !important;
}
.diygw-col-offset-17 {
  margin-left: 70.8333333333%;
}
.diygw-col-pull-17 {
  position: relative;
  right: 70.8333333333%;
}
.diygw-col-push-17 {
  position: relative;
  left: 70.8333333333%;
}
.diygw-col-18 {
  width: 75% !important;
}
.diygw-col-offset-18 {
  margin-left: 75%;
}
.diygw-col-pull-18 {
  position: relative;
  right: 75%;
}
.diygw-col-push-18 {
  position: relative;
  left: 75%;
}
.diygw-col-19 {
  width: 79.1666666667% !important;
}
.diygw-col-offset-19 {
  margin-left: 79.1666666667%;
}
.diygw-col-pull-19 {
  position: relative;
  right: 79.1666666667%;
}
.diygw-col-push-19 {
  position: relative;
  left: 79.1666666667%;
}
.diygw-col-20 {
  width: 83.3333333333% !important;
}
.diygw-col-offset-20 {
  margin-left: 83.3333333333%;
}
.diygw-col-pull-20 {
  position: relative;
  right: 83.3333333333%;
}
.diygw-col-push-20 {
  position: relative;
  left: 83.3333333333%;
}
.diygw-col-21 {
  width: 87.5% !important;
}
.diygw-col-offset-21 {
  margin-left: 87.5%;
}
.diygw-col-pull-21 {
  position: relative;
  right: 87.5%;
}
.diygw-col-push-21 {
  position: relative;
  left: 87.5%;
}
.diygw-col-22 {
  width: 91.6666666667% !important;
}
.diygw-col-offset-22 {
  margin-left: 91.6666666667%;
}
.diygw-col-pull-22 {
  position: relative;
  right: 91.6666666667%;
}
.diygw-col-push-22 {
  position: relative;
  left: 91.6666666667%;
}
.diygw-col-23 {
  width: 95.8333333333% !important;
}
.diygw-col-offset-23 {
  margin-left: 95.8333333333%;
}
.diygw-col-pull-23 {
  position: relative;
  right: 95.8333333333%;
}
.diygw-col-push-23 {
  position: relative;
  left: 95.8333333333%;
}
.diygw-col-24 {
  width: 100% !important;
}
.diygw-col-offset-24 {
  margin-left: 100%;
}
.diygw-col-pull-24 {
  position: relative;
  right: 100%;
}
.diygw-col-push-24 {
  position: relative;
  left: 100%;
}
/* -- 阴影 -- */
.diygw-shadow {
  box-shadow: 0 1rpx 6px var(--blackShadow);
}
.diygw-shadow[class*=white] {
  --ShadowSize: 0 1rpx 6px;
}
.diygw-shadow-lg {
  --ShadowSize: 0rpx 40rpx 100rpx 0rpx;
}
.diygw-shadow-warp {
  position: relative;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}
.diygw-shadow-warp:before,
.diygw-shadow-warp:after {
  position: absolute;
  content: "";
  top: 20rpx;
  bottom: 30rpx;
  left: 20rpx;
  width: 50%;
  box-shadow: 0 30rpx 20rpx rgba(0, 0, 0, 0.2);
  transform: rotate(-3deg);
  z-index: -1;
}
.diygw-shadow-warp:after {
  right: 20rpx;
  left: auto;
  transform: rotate(3deg);
}
.diygw-shadow-blur {
  position: relative;
}
.diygw-shadow-blur::before {
  content: "";
  display: block;
  background: inherit;
  filter: blur(10rpx);
  position: absolute;
  width: 100%;
  height: 100%;
  top: 10rpx;
  left: 10rpx;
  z-index: -1;
  opacity: 0.3;
  transform-origin: 0 0;
  border-radius: inherit;
  transform: scale(1, 1);
}
/* ==================
          操作条
 ==================== */
.diygw-bottom {
  position: fixed !important;
  left: 0;
  bottom: 0;
  width: 100%;
  background: #fff;
  z-index: 999999;
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.diygw-bar {
  display: flex;
  position: relative;
  align-items: center;
  min-height: 100rpx;
  justify-content: space-between;
}
.diygw-bar .action {
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: center;
  max-width: 100%;
}
.diygw-bar .action.border-title {
  position: relative;
  top: -10rpx;
}
.diygw-bar .action.border-title text[class*=bg-]:last-child {
  position: absolute;
  bottom: -0.5rem;
  min-width: 2rem;
  height: 6rpx;
  left: 0;
}
.diygw-bar .action.sub-title {
  position: relative;
  top: -0.2rem;
}
.diygw-bar .action.sub-title text {
  position: relative;
  z-index: 1;
}
.diygw-bar .action.sub-title text[class*=bg-]:last-child {
  position: absolute;
  display: inline-block;
  bottom: -0.2rem;
  border-radius: 6rpx;
  width: 100%;
  height: 0.6rem;
  left: 0.6rem;
  opacity: 0.3;
  z-index: 0;
}
.diygw-bar .action.sub-title text[class*=text-]:last-child {
  position: absolute;
  display: inline-block;
  bottom: -0.7rem;
  left: 0.5rem;
  opacity: 0.2;
  z-index: 0;
  text-align: right;
  font-weight: 900;
  font-size: 36rpx;
}
.diygw-bar.justify-center .action.border-title text:last-child,
.diygw-bar.justify-center .action.sub-title text:last-child {
  left: 0;
  right: 0;
  margin: auto;
  text-align: center;
}
.diygw-bar .action:first-child {
  margin-left: 30rpx;
  font-size: 30rpx;
}
.diygw-bar .action text.text-cut {
  text-align: left;
  width: 100%;
}
.diygw-bar .diygw-avatar:first-child {
  margin-left: 20rpx;
}
.diygw-bar .action:first-child > text[class*=diygwIcon-] {
  margin-left: -0.3em;
  margin-right: 0.3em;
}
.diygw-bar .action:last-child {
  margin-right: 30rpx;
}
.diygw-bar .action > text[class*=diygwIcon-],
.diygw-bar .action > view[class*=diygwIcon-] {
  font-size: 36rpx;
}
.diygw-bar .action > text[class*=diygwIcon-] + text[class*=diygwIcon-] {
  margin-left: 0.5em;
}
.diygw-bar .content {
  position: absolute;
  text-align: center;
  width: calc(100% - 340rpx);
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  margin: auto;
  height: 60rpx;
  font-size: 32rpx;
  line-height: 60rpx;
  cursor: none;
  pointer-events: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.diygw-bar.ios .content {
  bottom: 14rpx;
  height: 60rpx;
  font-size: 32rpx;
  line-height: 60rpx;
}
.diygw-bar.btn-group {
  justify-content: space-around;
}
.diygw-bar.btn-group button {
  padding: 20rpx 32rpx;
}
.diygw-bar.btn-group button {
  flex: 1;
  margin: 0 20rpx;
  max-width: 50%;
}
.diygw-bar .search-form {
  background-color: #f5f5f5;
  line-height: 64rpx;
  height: 64rpx;
  font-size: 24rpx;
  color: var(--black);
  flex: 1;
  display: flex;
  align-items: center;
  margin: 0 30rpx;
}
.diygw-bar .search-form + .action {
  margin-right: 30rpx;
}
.diygw-bar .search-form input {
  flex: 1;
  padding-right: 30rpx;
  height: 64rpx;
  line-height: 64rpx;
  font-size: 16rpx;
  background-color: transparent;
}
.diygw-bar .search-form [class*=diygwIcon-] {
  margin: 0 0.5em 0 0.8em;
}
.diygw-bar .search-form [class*=diygwIcon-]::before {
  top: 0px;
}
.diygw-bar.fixed, .nav.fixed {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1024;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}
.diygw-bar.foot {
  position: fixed;
  width: 100%;
  bottom: 0;
  z-index: 1024;
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}
.diygw-bar.tabbar {
  padding: 0;
  height: calc(100rpx + env(safe-area-inset-bottom) / 2);
  padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}
.diygw-tabbar-height {
  min-height: 100rpx;
  height: calc(100rpx + env(safe-area-inset-bottom) / 2);
}
.diygw-bar.tabbar.shadow {
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}
.diygw-bar.tabbar .action {
  font-size: 24rpx;
  position: relative;
  flex: 1;
  text-align: center;
  padding: 0;
  display: block;
  height: auto;
  line-height: 1;
  margin: 0;
  overflow: initial;
}
.diygw-bar.tabbar.shop .action {
  width: 280px;
  flex: initial;
}
.diygw-bar.tabbar .action.add-action {
  position: relative;
  z-index: 2;
  padding-top: 50rpx;
  background-color: inherit;
}
.diygw-bar.tabbar .action.add-action [class*=diygwIcon-] {
  position: absolute;
  width: 70rpx;
  z-index: 2;
  height: 70rpx;
  border-radius: 50%;
  line-height: 70rpx;
  font-size: 50rpx;
  top: -35rpx;
  left: 0;
  right: 0;
  margin: auto;
  padding: 0;
}
.diygw-bar.tabbar .action.add-action::after {
  content: "";
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  top: -50rpx;
  left: 0;
  right: 0;
  margin: auto;
  box-shadow: 0 -3rpx 8rpx rgba(0, 0, 0, 0.08);
  border-radius: 50rpx;
  background-color: inherit;
  z-index: 0;
}
.diygw-bar.tabbar .action.add-action::before {
  content: "";
  position: absolute;
  width: 100rpx;
  height: 30rpx;
  bottom: 30rpx;
  left: 0;
  right: 0;
  margin: auto;
  background-color: inherit;
  z-index: 1;
}
.diygw-bar.tabbar .btn-group {
  flex: 1;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 10rpx;
}
.diygw-bar.tabbar button.action::after {
  border: 0;
}
.diygw-bar.tabbar .action [class*=diygwIcon-] {
  width: 100rpx;
  position: relative;
  display: block;
  height: auto;
  margin: 0 auto 10rpx;
  text-align: center;
  font-size: 40px;
}
.diygw-bar.tabbar .action .diygwIcon-diygw-image {
  margin: 0 auto;
}
.diygw-bar.tabbar .action .diygwIcon-diygw-image image {
  width: 50rpx;
  height: 50rpx;
  display: inline-block;
}
.diygw-bar.tabbar .submit {
  align-items: center;
  display: flex;
  justify-content: center;
  text-align: center;
  position: relative;
  flex: 2;
  align-self: stretch;
}
.diygw-bar.tabbar .submit:last-child {
  flex: 2.6;
}
.diygw-bar.tabbar .submit + .submit {
  flex: 2;
}
.diygw-bar.tabbar.border .action::before {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(0.5);
  transform-origin: 0 0;
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
  z-index: 3;
}
.diygw-bar.tabbar.border .action:last-child:before {
  display: none;
}
.diygw-bar.input {
  padding-right: 20rpx;
  background-color: var(--white);
}
.diygw-bar.input input {
  overflow: initial;
  line-height: 64rpx;
  height: 64rpx;
  min-height: 64rpx;
  flex: 1;
  font-size: 30rpx;
  margin: 0 20rpx;
}
.diygw-bar.input .action {
  margin-left: 20rpx;
}
.diygw-bar.input .action [class*=diygwIcon-] {
  font-size: 48rpx;
}
.diygw-bar.input input + .action {
  margin-right: 20rpx;
  margin-left: 0px;
}
.diygw-bar.input .action:first-child [class*=diygwIcon-] {
  margin-left: 0px;
}
/* ==================
          按钮
 ==================== */
.diygw-btn, .diygw-btn-default {
  position: relative;
  border: 0px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  overflow: visible;
  border-radius: 0;
  transform: translate(0px, 0px);
}
.diygw-btn-default {
  padding: 0px;
  margin: 0px;
}
.diygw-btn-default::after, .diygw-btn::after {
  display: none;
}
.diygw-btn:not([class*=bg-]) {
  background-color: transparent;
}
.diygw-btn[class*=line] {
  background-color: transparent;
}
.diygw-btn[class*=line]::after {
  content: " ";
  display: block;
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1rpx solid currentColor;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: 6px;
  z-index: 1;
  pointer-events: none;
}
.diygw-btn[class*=lines]::after {
  border: 6rpx solid currentColor;
}
.diygw-btn[class*=bg-]::after {
  display: none;
}
.diygw-btn .diygw-btn-icon {
  border-radius: 500px;
  padding: 0;
  margin-right: 6rpx;
  font-size: inherit;
}
.diygw-btn.button-hover {
  transform: translate(1rpx, 1rpx);
}
.block {
  display: block;
}
.diygw-btn.block {
  display: flex;
  width: 100%;
}
.diygw-btn[disabled] {
  opacity: 0.6;
}
.button-icon {
  font-size: inherit;
}
.diygw-btn {
  padding: 0 20rpx;
  font-size: 14px;
  height: 64rpx;
}
.diygw-btn.diygw-icon {
  width: 64rpx;
  height: 64rpx;
}
.diygw-btn.xs {
  padding: 0 10rpx;
  font-size: 20rpx;
  height: 48rpx;
}
.diygw-btn.diygw-icon.xs {
  width: 20rpx;
  height: 20rpx;
}
.diygw-btn.sm {
  padding: 0 20rpx;
  font-size: 24rpx;
  height: 56rpx;
}
.diygw-btn.diygw-icon.sm {
  width: 24rpx;
  height: 24rpx;
}
.diygw-btn.md {
  padding: 0 24rpx;
  font-size: 14px;
  height: 64rpx;
}
.diygw-btn.diygw-icon.md {
  width: 14px;
  height: 14px;
}
.diygw-btn.lg {
  padding: 0 40rpx;
  font-size: 48rpx;
  height: 40px;
}
.diygw-btn.diygw-icon.lg {
  width: 48rpx;
  height: 48rpx;
}
.diygw-btn.xl {
  padding: 0 60rpx;
  font-size: 64rpx;
  height: 60px;
}
.diygw-btn.diygw-icon.xl {
  width: 64rpx;
  height: 64rpx;
}
.radius, .radius[class*=line]::after {
  border-radius: 2000px;
}
.radius-xs {
  border-radius: 6rpx;
}
.radius-xs[class*=line]::after {
  border-radius: 6px;
}
.radius-sm {
  border-radius: 16rpx;
}
.radius-sm[class*=line]::after {
  border-radius: 32rpx;
}
.radius-md {
  border-radius: 24rpx;
}
.radius-md[class*=line]::after {
  border-radius: 48rpx;
}
.radius-lg {
  border-radius: 32rpx;
}
.radius-lg[class*=line]::after {
  border-radius: 64rpx;
}
.radius-xl {
  border-radius: 200px;
}
.radius-xl[class*=line]::after {
  border-radius: 400px;
}
.radius-left, .radius-left[class*=line]::after {
  border-top-left-radius: 2000px !important;
  border-bottom-left-radius: 2000px !important;
}
.radius-right, .radius-right[class*=line]::after {
  border-top-right-radius: 2000px !important;
  border-bottom-right-radius: 2000px !important;
}
.radius-top, .radius-top[class*=line]::after {
  border-top-left-radius: 2000px !important;
  border-top-right-radius: 2000px !important;
}
.radius-bottom, .radius-bottom[class*=line]::after {
  border-bottom-left-radius: 2000px !important;
  border-bottom-right-radius: 2000px !important;
}
/* ==================
          文本
 ==================== */
.text-red,
.diygw-line-red,
.diygw-lines-red {
  color: var(--red);
}
.diygw-line-red::after,
.diygw-lines-red::after {
  border-color: var(--red);
}
.text-shadow[class*=-red] {
  text-shadow: var(--ShadowSize) var(--redShadow);
}
.text-orange,
.diygw-line-orange,
.diygw-lines-orange {
  color: var(--orange);
}
.diygw-line-orange::after,
.diygw-lines-orange::after {
  border-color: var(--orange);
}
.text-shadow[class*=-orange] {
  text-shadow: var(--ShadowSize) var(--orangeShadow);
}
.text-yellow,
.diygw-line-yellow,
.diygw-lines-yellow {
  color: var(--yellow);
}
.diygw-line-yellow::after,
.diygw-lines-yellow::after {
  border-color: var(--yellow);
}
.text-shadow[class*=-yellow] {
  text-shadow: var(--ShadowSize) var(--yellowShadow);
}
.text-olive,
.diygw-line-olive,
.diygw-lines-olive {
  color: var(--olive);
}
.diygw-line-olive::after,
.diygw-lines-olive::after {
  border-color: var(--olive);
}
.text-shadow[class*=-olive] {
  text-shadow: var(--ShadowSize) var(--oliveShadow);
}
.text-green,
.diygw-line-green,
.diygw-lines-green {
  color: var(--green);
}
.diygw-line-green::after,
.diygw-lines-green::after {
  border-color: var(--green);
}
.text-shadow[class*=-green] {
  text-shadow: var(--ShadowSize) var(--greenShadow);
}
.text-cyan,
.diygw-line-cyan,
.diygw-lines-cyan {
  color: var(--cyan);
}
.diygw-line-cyan::after,
.diygw-lines-cyan::after {
  border-color: var(--cyan);
}
.text-shadow[class*=-cyan] {
  text-shadow: var(--ShadowSize) var(--cyanShadow);
}
.text-blue,
.diygw-line-blue,
.diygw-lines-blue {
  color: var(--blue);
}
.diygw-line-blue::after,
.diygw-lines-blue::after {
  border-color: var(--blue);
}
.text-shadow[class*=-blue] {
  text-shadow: var(--ShadowSize) var(--blueShadow);
}
.text-purple,
.diygw-line-purple,
.diygw-lines-purple {
  color: var(--purple);
}
.diygw-line-purple::after,
.diygw-lines-purple::after {
  border-color: var(--purple);
}
.text-shadow[class*=-purple] {
  text-shadow: var(--ShadowSize) var(--purpleShadow);
}
.text-mauve,
.diygw-line-mauve,
.diygw-lines-mauve {
  color: var(--mauve);
}
.diygw-line-mauve::after,
.diygw-lines-mauve::after {
  border-color: var(--mauve);
}
.text-shadow[class*=-mauve] {
  text-shadow: var(--ShadowSize) var(--mauveShadow);
}
.text-pink,
.diygw-line-pink,
.diygw-lines-pink {
  color: var(--pink);
}
.diygw-line-pink::after,
.diygw-lines-pink::after {
  border-color: var(--pink);
}
.text-shadow[class*=-pink] {
  text-shadow: var(--ShadowSize) var(--pinkShadow);
}
.text-brown,
.diygw-line-brown,
.diygw-lines-brown {
  color: var(--brown);
}
.diygw-line-brown::after,
.diygw-lines-brown::after {
  border-color: var(--brown);
}
.text-shadow[class*=-brown] {
  text-shadow: var(--ShadowSize) var(--brownShadow);
}
.text-grey,
.diygw-line-grey,
.diygw-lines-grey {
  color: var(--grey);
}
.diygw-line-grey::after,
.diygw-lines-grey::after {
  border-color: var(--grey);
}
.text-shadow[class*=-grey] {
  text-shadow: var(--ShadowSize) var(--greyShadow);
}
.text-black,
.diygw-line-black,
.diygw-lines-black {
  color: var(--black);
}
.diygw-line-black::after,
.diygw-lines-black::after {
  border-color: var(--black);
}
.text-shadow[class*=-black] {
  text-shadow: var(--ShadowSize) var(--blackShadow);
}
.text-white,
.diygw-line-white,
.diygw-lines-white {
  color: var(--white);
}
.diygw-line-white::after,
.diygw-lines-white::after {
  border-color: var(--white);
}
.text-shadow[class*=-white] {
  text-shadow: var(--ShadowSize) var(--whiteShadow);
}
.text-none,
.diygw-line-none,
.diygw-lines-none {
  color: var(--none);
}
.diygw-line-none::after,
.diygw-lines-none::after {
  border-color: var(--none);
}
.text-shadow[class*=-none] {
  text-shadow: var(--ShadowSize) var(--noneShadow);
}
.diygw-text {
  font-size: 14px;
}
.text-xs,
.diygw-line-xs,
.diygw-lines-xs {
  color: var(--xs);
}
.diygw-line-xs::after,
.diygw-lines-xs::after {
  border-color: var(--xs);
}
.text-shadow[class*=-xs] {
  text-shadow: var(--ShadowSize) var(--xsShadow);
}
.diygw-text-xs {
  font-size: 20rpx;
}
.text-sm,
.diygw-line-sm,
.diygw-lines-sm {
  color: var(--sm);
}
.diygw-line-sm::after,
.diygw-lines-sm::after {
  border-color: var(--sm);
}
.text-shadow[class*=-sm] {
  text-shadow: var(--ShadowSize) var(--smShadow);
}
.diygw-text-sm {
  font-size: 24rpx;
}
.text-md,
.diygw-line-md,
.diygw-lines-md {
  color: var(--md);
}
.diygw-line-md::after,
.diygw-lines-md::after {
  border-color: var(--md);
}
.text-shadow[class*=-md] {
  text-shadow: var(--ShadowSize) var(--mdShadow);
}
.diygw-text-md {
  font-size: 14px;
}
.text-lg,
.diygw-line-lg,
.diygw-lines-lg {
  color: var(--lg);
}
.diygw-line-lg::after,
.diygw-lines-lg::after {
  border-color: var(--lg);
}
.text-shadow[class*=-lg] {
  text-shadow: var(--ShadowSize) var(--lgShadow);
}
.diygw-text-lg {
  font-size: 48rpx;
}
.text-xl,
.diygw-line-xl,
.diygw-lines-xl {
  color: var(--xl);
}
.diygw-line-xl::after,
.diygw-lines-xl::after {
  border-color: var(--xl);
}
.text-shadow[class*=-xl] {
  text-shadow: var(--ShadowSize) var(--xlShadow);
}
.diygw-text-xl {
  font-size: 64rpx;
}
.text-cut {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.text-bold {
  font-weight: bold;
}
.text-center {
  text-align: center;
}
.text-content {
  line-height: 1.6;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.diygw-text-line1 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical !important;
}
.diygw-text-line2 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.diygw-text-line3 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.diygw-text-line4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.diygw-text-line5 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
/*  -- flex弹性布局 -- */
.flex {
  display: flex;
}
.basis-xs {
  flex-basis: 20%;
}
.basis-sm {
  flex-basis: 40%;
}
.basis-df {
  flex-basis: 50%;
}
.basis-lg {
  flex-basis: 60%;
}
.basis-xl {
  flex-basis: 80%;
}
.flex1, .flex-sub {
  flex: 1;
}
.flex-twice {
  flex: 2;
}
.flex-treble {
  flex: 3;
}
.flex-direction-column {
  flex-direction: column;
}
.flex-direction-column .flex1 {
  flex: inherit;
}
.flex-direction-row-reverse {
  flex-direction: row-reverse;
}
.flex-direction-column-reverse {
  flex-direction: column-reverse;
}
.flex-direction-column-reverse .flex1 {
  flex: inherit;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}
.align-start, .items-start {
  align-items: flex-start;
}
.align-end, .items-end {
  align-items: flex-end;
}
.align-center, .items-center {
  align-items: center;
}
.align-stretch, .items-stretch {
  align-items: stretch;
}
.align-baseline, .items-baseline {
  align-items: baseline;
}
.self-start {
  align-self: flex-start;
}
.self-center {
  align-self: flex-center;
}
.self-end {
  align-self: flex-end;
}
.self-stretch {
  align-self: stretch;
}
.align-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.items-end {
  align-items: flex-end;
}
.scroll-view {
  overflow-x: auto;
  flex-wrap: nowrap;
  display: flex;
  flex-direction: row !important;
}
.scroll-view > .flex {
  overflow-x: auto;
}
.scroll-view [class*=diygw-col-] {
  flex-shrink: 0;
}
.scroll-y {
  flex-wrap: nowrap !important;
  display: flex !important;
  flex-direction: column !important;
}
.flex-content {
  float: none !important;
  flex-wrap: wrap;
}
/* ==================
         表单
 ==================== */
.diygw-form, uni-form > span {
  flex-wrap: wrap;
  width: 100%;
}
.diygw-form .u-form {
  flex-wrap: wrap;
  width: 100%;
  display: flex;
}
/* ==================
         表单
 ==================== */
uni-input {
  font-size: 14px;
}
.diygw-form-item {
  width: 100%;
  padding: 20rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.diygw-form-item.diygw-col-0 {
  width: auto;
}
.diygw-form-item.diygw-form-item-small {
  padding: 0px 12rpx;
}
.diygw-form-item.diygw-form-item-small [class*=diy-icon-] {
  height: auto !important;
}
.diygw-form-item.diygw-form-item-small:after {
  right: 10rpx;
  left: 10rpx;
}
.diygw-form-item.diygw-form-item-small.diygw-form-item-notpadding {
  padding: 0px;
}
.diygw-form-item.diygw-form-item-notpadding {
  padding: 0px;
}
.diygw-form-item:after {
  position: absolute;
  box-sizing: border-box;
  content: " ";
  pointer-events: none;
  right: 24rpx;
  bottom: 0;
  left: 24rpx;
  border-bottom: 1px solid #ebedf0;
  transform: scaleY(0.5);
}
.diygw-form-item.noborder::after {
  display: none;
}
.diygw-form-item .title {
  text-align: left;
  width: var(--form-label-width);
  padding: 4px 0;
  font-weight: bold;
  word-wrap: break-word;
  margin-right: 24rpx;
  position: relative;
}
.diygw-form-item .title.title-mb5 {
  margin-bottom: 10rpx;
}
.diygw-form-item .input {
  flex: 1;
  display: flex;
  box-sizing: border-box;
  align-items: center;
  width: 100%;
  min-width: 0;
  margin: 0;
  padding: 12rpx 0px;
  color: var(--black);
  line-height: inherit;
  text-align: left;
  background-color: transparent;
  resize: none;
  position: relative;
}
.diygw-form-item .input .diygw-tag, .diygw-form-item .input [class*=diy-icon-] {
  margin-right: 10rpx;
  max-height: 48rpx;
}
.diygw-form-item .input .icon-right {
  width: 50rpx;
}
.diygw-form-item .input .diygw-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 10rpx;
}
.diygw-form-item .input.flex {
  padding-right: 0px;
  display: flex;
}
.diygw-form-item .input.flex .flex1 {
  width: 100%;
}
.diygw-form-item textarea {
  height: 4.6em;
  width: 100%;
  flex: 1;
  resize: none;
}
.diygw-form-item text[class*=diygwIcon-] {
  font-size: 36rpx;
  padding: 0;
  box-sizing: border-box;
}
.diygw-form-item .align-start .title {
  height: 1em;
  margin-top: 32rpx;
  line-height: 1em;
}
.diygw-form-item .input {
  padding: 12rpx 0;
}
.diygw-form-item .input.solid {
  padding: 20rpx;
}
.diygw-form-item .input.solid:after {
  border-radius: 16rpx;
}
.diygw-form-item .input.solid.radius {
  overflow: hidden;
}
.diygw-form-item .input.solid.radius:after {
  border-radius: 50px;
}
.diygw-form-item.flex-direction-column {
  align-items: flex-start;
}
.diygw-form-item.flex-direction-column .title {
  margin-right: 0px;
  margin-bottom: 0px;
}
.diygw-form-item picker {
  flex: 1;
  padding-right: 40rpx;
  overflow: hidden;
  position: relative;
}
.diygw-form-item picker .picker-item {
  height: 48rpx;
  line-height: 48rpx;
}
.diygw-form-item picker::after {
  font-family: "diygwui";
  display: block;
  content: "\e71a";
  position: absolute;
  font-size: 28rpx;
  width: 30rpx;
  color: #c0c4cc;
  text-align: center;
  top: 6rpx;
  bottom: 0;
  right: 0;
  margin: auto;
}
.diygw-form-item textarea[disabled],
.diygw-form-item textarea[disabled] .placeholder {
  color: transparent;
}
.upload {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 160rpx;
  height: 160rpx;
  margin: 0 16rpx 16rpx 0;
  background: #f7f8fa;
  border-radius: 4rpx;
  overflow: hidden;
}
.upload .image {
  width: 160rpx;
  height: 160rpx;
}
.upload .diy-icon-close {
  position: absolute;
  right: 0;
  top: 0;
  display: block;
  width: 36rpx;
  font-size: 36rpx;
  height: 36rpx;
  color: #FFF;
  background: rgba(0, 0, 0, 0.88);
  border-radius: 0 0 0 12rpx;
}
.diygw-autoview .diygw-absolute {
  display: flex;
  overflow: hidden;
  align-items: stretch;
}
/* ==================
          徽章
 ==================== */
.diygw-tag {
  vertical-align: middle;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  font-family: Helvetica Neue, Helvetica, sans-serif;
  white-space: nowrap;
}
.diygw-tag uni-text {
  white-space: nowrap;
}
.diygw-tag.badge:not([class*=bg]):not([class*=line]) {
  background-color: #dd514c;
  color: var(--white);
}
.diygw-tag {
  font-size: 24rpx;
  padding: 0 20rpx;
  height: 64rpx;
}
.diygw-tag .diygw-tag-img {
  height: 24px;
  width: 24px;
}
.diygw-capsule {
  padding: 0;
}
.diygw-tag.xs {
  font-size: 20rpx;
  padding: 0 10rpx;
  height: 48rpx;
}
.diygw-tag.xs .diygw-tag-img {
  height: 20px;
  width: 20px;
}
.diygw-tag.sm {
  font-size: 24rpx;
  padding: 0 16rpx;
  height: 56rpx;
}
.diygw-tag.sm .diygw-tag-img {
  height: 24px;
  width: 24px;
}
.diygw-tag.md {
  font-size: 14px;
  padding: 0 24rpx;
  height: 64rpx;
}
.diygw-tag.md .diygw-tag-img {
  height: 28px;
  width: 28px;
}
.diygw-tag.lg {
  font-size: 48rpx;
  padding: 0 32rpx;
  height: 40px;
}
.diygw-tag.lg .diygw-tag-img {
  height: 32px;
  width: 32px;
}
.diygw-tag.xl {
  font-size: 64rpx;
  padding: 0 48rpx;
  height: 60px;
}
.diygw-tag.xl .diygw-tag-img {
  height: 48px;
  width: 48px;
}
.diygw-tag.badge[class*=icon] {
  min-width: 32rpx;
  min-height: 32rpx;
  font-size: 24rpx !important;
}
.diygw-tag + .diygw-tag {
  margin-left: 10;
}
.diygw-capsule {
  display: inline-flex;
  vertical-align: middle;
}
.diygw-capsule.diygw-tag {
  height: auto;
}
.diygw-capsule + .diygw-capsule {
  margin-left: 20rpx;
}
.diygw-capsule .diygw-tag {
  margin: 0;
}
.diygw-tag[class*=line-]::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1rpx solid currentColor;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: inherit;
  z-index: 1;
  pointer-events: none;
}
.diygw-tag[class*=lines-]::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 2rpx solid currentColor;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: inherit;
  z-index: 1;
  pointer-events: none;
}
.diygw-capsule .diygw-tag[class*=line-]:last-child::after {
  border-left: 0 solid transparent;
}
.diygw-capsule .diygw-tag[class*=line-]:first-child::after {
  border-right: 0 solid transparent;
}
.diygw-capsule .diygw-tag:first-child {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.diygw-capsule .diygw-tag:last-child::after,
.diygw-capsule .diygw-tag[class*=line-] {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.diygw-tag.badge {
  border-radius: 200px;
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  padding: 0;
  height: 14px;
  min-width: 14px;
}
.diygw-tag.badge.empty {
  padding: 0;
  right: -8rpx;
  top: -8rpx;
  width: 16rpx;
  height: 16rpx;
  min-width: auto;
}
.diygw-tag[class*=diy-icon-] {
  min-width: 32rpx;
  min-height: 32rpx;
}
/* ==================
         开关
 ==================== */
.diygw-checkbox, .diygw-radio {
  -webkit-appearance: none;
  width: 0;
  height: 0;
  display: none;
  border: none;
  float: left;
}
.diygw-checkbox-label, .diygw-radio-label {
  display: flex;
  max-width: 100%;
  margin-bottom: 5px;
  margin-right: 5px;
  align-items: center;
}
.diygw-icon-radio {
  border-radius: 100%;
  width: 18px;
  height: 18px;
  margin-right: 5px;
  border: 1px solid #b8b8b8;
  vertical-align: top;
}
.diygw-icon-radio.checked {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMDE0IDc5LjE1Njc5NywgMjAxNC8wOC8yMC0wOTo1MzowMiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjA3QUE4OTlFQjJCODExRTVBRkM2RDBFMDhDRDA3MTJFIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjA3QUE4OTlGQjJCODExRTVBRkM2RDBFMDhDRDA3MTJFIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MDdBQTg5OUNCMkI4MTFFNUFGQzZEMEUwOENEMDcxMkUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MDdBQTg5OURCMkI4MTFFNUFGQzZEMEUwOENEMDcxMkUiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5LqQGPAAAA7ElEQVR42uyXTQrCMBCFG+tKRY/gz0naXlFc2HqUKvQk4hEUVDCVcQIBu5HMTFK6MA8+KCWZeW2SV6oAIBlSo2RgRQPRQDQw9pg7Rdb2+ozcRVVMEDHJkBrR8JW29zJuPc7gFNmCW6UdG9zAHugqQxvIga88pIGjwEBNqa0IX0Oz269Iytzfb2ThOh2UHFgKmid2zipEELUeWaFdAyhLMEFuwiWYIw/fN2AKNIKnb1zNOUlYCE5BETqIKkbzQ19RvCM0r/qK4m4qmmBqO03N9Ymaftwg+qUZskFeyAV5Soqo+F8QDUQDf2/gI8AAIiHXffupwGIAAAAASUVORK5CYII=);
  background-size: 100%;
  border-radius: 100%;
  background-color: #04be02;
  border: 1px solid #04be02;
}
.diygw-icon-checkbox {
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  border: 1px solid #b8b8b8;
  margin-right: 5px;
  vertical-align: top;
}
.diygw-icon-checkbox.checked {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMDE0IDc5LjE1Njc5NywgMjAxNC8wOC8yMC0wOTo1MzowMiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkY0MTI1RkFBQjJCNzExRTU5NzE3RDMyNDM3NTgzRTE4IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkY0MTI1RkFCQjJCNzExRTU5NzE3RDMyNDM3NTgzRTE4Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6RjQxMjVGQThCMkI3MTFFNTk3MTdEMzI0Mzc1ODNFMTgiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6RjQxMjVGQTlCMkI3MTFFNTk3MTdEMzI0Mzc1ODNFMTgiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5UDJTUAAABUElEQVR42mL8//8/w0ACJoYBBqMOGHEOsB0oB/AC8UIgPgTEZvR2gDYQnwXiOChfH0UWVA7QEPsA8af/CDAJXQ0tLS8G4r9Ili8BYkZ6OaD9Pyo4AcQc2NRS22ImIJ6OZvltIBbFpYealjMD8WI0y78CsS4+fdS0fNl/TBBOSC8uCVBiyQViASItX4HF8j5iHI/LwIVQQ+4CsQEeA1iAeBUWyw9AzSHLAS1ohn0H4hQs6liBeDUWy58AsTix0YdNUBiIz2AxeD4Qc0HVsAHxOixqfgKxNSnpB5cEPxAfw2LBeWiq3vofO8gjNQHjk+QF4kNYLPmHw/Kl5OQgQgpAQb77P2FwDil6qOoABmgRuhWP5a+AWJbcMoRYhaBEtx5HorOjpBAjRTEo261Ec0AKpaUoOUUurJDqpEYxTm6Nl05sSUcIM472jEYdMOIdABBgAFfZNpPCdKCTAAAAAElFTkSuQmCC);
  background-size: 100%;
  background-color: #04be02;
  border: 1px solid #04be02;
}
.diygw-switch-box {
  position: relative;
  width: 42px;
  height: 22px;
  border: 1px solid #DFDFDF;
  outline: 0;
  border-radius: 16px;
  box-sizing: border-box;
  background-color: #DFDFDF;
  transition: background-color 0.1s, border 0.1s;
}
.diygw-switch-box:before {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 20px;
  border-radius: 15px;
  background-color: #FDFDFD;
  transition: transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
}
.diygw-switch-box:after {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  border-radius: 15px;
  background-color: #FFFFFF;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.4);
  transition: transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
}
.diygw-switch-box.checked {
  border-color: #04BE02;
  background-color: #04BE02;
}
.diygw-switch {
  position: absolute;
  left: -9999px;
}
.diygw-switch-box.checked:before {
  transform: scale(0);
}
.diygw-switch-box.checked:after {
  transform: translateX(20px);
}
.check-red .diygw-icon-radio.checked, .check-red .diygw-icon-checkbox.checked, .check-red .diygw-switch-box.checked {
  border-color: var(--red) !important;
  background-color: var(--red) !important;
}
.check-orange .diygw-icon-radio.checked, .check-orange .diygw-icon-checkbox.checked, .check-orange .diygw-switch-box.checked {
  border-color: var(--orange) !important;
  background-color: var(--orange) !important;
}
.check-yellow .diygw-icon-radio.checked, .check-yellow .diygw-icon-checkbox.checked, .check-yellow .diygw-switch-box.checked {
  border-color: var(--yellow) !important;
  background-color: var(--yellow) !important;
}
.check-olive .diygw-icon-radio.checked, .check-olive .diygw-icon-checkbox.checked, .check-olive .diygw-switch-box.checked {
  border-color: var(--olive) !important;
  background-color: var(--olive) !important;
}
.check-green .diygw-icon-radio.checked, .check-green .diygw-icon-checkbox.checked, .check-green .diygw-switch-box.checked {
  border-color: var(--green) !important;
  background-color: var(--green) !important;
}
.check-cyan .diygw-icon-radio.checked, .check-cyan .diygw-icon-checkbox.checked, .check-cyan .diygw-switch-box.checked {
  border-color: var(--cyan) !important;
  background-color: var(--cyan) !important;
}
.check-blue .diygw-icon-radio.checked, .check-blue .diygw-icon-checkbox.checked, .check-blue .diygw-switch-box.checked {
  border-color: var(--blue) !important;
  background-color: var(--blue) !important;
}
.check-purple .diygw-icon-radio.checked, .check-purple .diygw-icon-checkbox.checked, .check-purple .diygw-switch-box.checked {
  border-color: var(--purple) !important;
  background-color: var(--purple) !important;
}
.check-mauve .diygw-icon-radio.checked, .check-mauve .diygw-icon-checkbox.checked, .check-mauve .diygw-switch-box.checked {
  border-color: var(--mauve) !important;
  background-color: var(--mauve) !important;
}
.check-pink .diygw-icon-radio.checked, .check-pink .diygw-icon-checkbox.checked, .check-pink .diygw-switch-box.checked {
  border-color: var(--pink) !important;
  background-color: var(--pink) !important;
}
.check-brown .diygw-icon-radio.checked, .check-brown .diygw-icon-checkbox.checked, .check-brown .diygw-switch-box.checked {
  border-color: var(--brown) !important;
  background-color: var(--brown) !important;
}
.check-grey .diygw-icon-radio.checked, .check-grey .diygw-icon-checkbox.checked, .check-grey .diygw-switch-box.checked {
  border-color: var(--grey) !important;
  background-color: var(--grey) !important;
}
.check-black .diygw-icon-radio.checked, .check-black .diygw-icon-checkbox.checked, .check-black .diygw-switch-box.checked {
  border-color: var(--black) !important;
  background-color: var(--black) !important;
}
.check-white .diygw-icon-radio.checked, .check-white .diygw-icon-checkbox.checked, .check-white .diygw-switch-box.checked {
  border-color: var(--white) !important;
  background-color: var(--white) !important;
}
/* grid布局 */
.diygw-grid {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}
.diygw-grid .diygw-grid-title {
  white-space: nowrap;
}
.diygw-grid.scroll-view {
  flex-wrap: nowrap;
  white-space: nowrap;
  overflow-x: auto;
}
.diygw-grid [class*=bg-] .diygw-avatar {
  color: #fff;
}
.diygw-grid uni-button, .diygw-grid button {
  color: inherit;
}
.diygw-grid-inner {
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-direction: column;
  text-align: center;
  padding: 20rpx;
  position: relative;
  background-position: center;
  background-repeat: no-repeat;
}
.diygw-grid-inner.border::after {
  position: absolute;
  box-sizing: border-box;
  content: " ";
  pointer-events: none;
  top: -50%;
  right: -50%;
  bottom: -50%;
  left: -50%;
  border: 0 solid #ebedf0;
  transform: scale(0.5);
  z-index: 1;
  border-width: 0 1rpx 1rpx 0;
}
.diygw-grid-icon {
  width: 48px;
  height: 48px;
}
.diygw-grid.col-1 .diygw-grid-item {
  width: 100%;
}
.diygw-grid.col-2 .diygw-grid-item {
  width: 50%;
}
.diygw-grid.col-3 .diygw-grid-item {
  width: 33.33%;
}
.diygw-grid.col-4 .diygw-grid-item {
  width: 25%;
}
.diygw-grid.col-5 .diygw-grid-item {
  width: 20%;
}
.diygw-grid .diygw-avatar {
  background-color: transparent;
  font-size: 80rpx;
  color: #333;
}
.diygw-grid .diygw-avatar .diygw-tag[class*=diy-icon-] {
  font-size: 20rpx;
}
.diygw-grid.diygw-actions {
  background: inherit;
  align-items: center;
}
.diygw-grid.diygw-actions button.diygw-action {
  border-radius: 0;
  border: 0px;
  background: none;
  position: relative;
  border: 0px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  overflow: visible;
  transform: translate(0px, 0px);
}
.diygw-grid.diygw-actions button.diygw-action.radius {
  border-radius: 2000px;
}
.diygw-grid.diygw-actions button.diygw-action::after {
  display: none;
}
.diygw-grid.diygw-actions .diygw-action {
  flex: 1;
  padding: 6rpx 0;
}
.diygw-grid.diygw-actions .diygw-action[class*=bg-] {
  flex: 2;
}
.diygw-grid.diygw-actions .diygw-action[class*=bg-] [class*=diy-icon-] {
  color: inherit;
}
.diygw-grid.diygw-actions .diygw-action.radius-right {
  margin-right: 10rpx;
}
.diygw-grid.diygw-actions .diygw-action.radius-left {
  margin-left: 10rpx;
}
.diygw-grid.diygw-actions .diygw-action.radius {
  margin-right: 10rpx;
  margin-left: 10rpx;
}
.diygw-grid.diygw-actions .diygw-action .diygw-grid-inner {
  padding: 0;
  flex: 1;
}
.diygw-grid.diygw-actions .diygw-action .diygw-avatar {
  width: 60rpx;
  height: 60rpx;
  font-size: 60rpx;
}
.diygw-grid.diygw-actions .diygw-action .diygw-grid-inner.border::after {
  border-width: 0 1rpx 0 0;
}
.diygw-grid.diygw-actions .diygw-action .diygw-grid-title.not-avatar {
  font-size: 16px;
  line-height: 80rpx;
}
.diygw-grid.diygw-actions .diygw-action.addon {
  position: relative;
  z-index: 2;
  padding-top: 60rpx;
  background-color: inherit;
}
.diygw-grid.diygw-actions .diygw-action.addon .diygw-grid-title {
  z-index: 2;
}
.diygw-grid.diygw-actions .diygw-action.addon .diygw-grid-icon {
  position: absolute;
  max-width: 70rpx;
  z-index: 2;
  max-height: 70rpx;
  border-radius: 50%;
  line-height: 70rpx;
  font-size: 50rpx;
  top: -64rpx;
  left: 0;
  right: 0;
  margin: auto;
  padding: 0;
}
.diygw-grid.diygw-actions .diygw-action.addon:before {
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  top: -24rpx;
  left: 0;
  right: 0;
  margin: auto;
  content: "";
  box-shadow: 0 -4rpx 8rpx rgba(0, 0, 0, 0.08);
  border-radius: 50rpx;
  background-color: inherit;
  z-index: 0;
}
.diygw-grid.diygw-actions .diygw-action:last-child .diygw-grid-inner.border::after, .diygw-grid.diygw-actions .diygw-action:last-child .diygw-grid-inner.border::solid-right {
  border-width: 0;
}
/* ==================
         卡片
 ==================== */
.diygw-card {
  overflow: hidden;
  margin: 16rpx;
  border-radius: 10rpx;
}
/* ==================
         轮播
 ==================== */
swiper .a-swiper-dot {
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  vertical-align: middle;
}
swiper[class*=-dot] .wx-swiper-dots,
swiper[class*=-dot] .a-swiper-dots,
swiper[class*=-dot] .uni-swiper-dots {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: center;
}
.swiper-indicator_rect [class*=-dot] {
  width: 32rpx !important;
  height: 14rpx !important;
  border-radius: 0;
}
.swiper-indicator_rect [class*=-dots] {
  width: 100% !important;
}
.swiper-indicator_rect_radius [class*=-dot-active] {
  width: 32rpx !important;
  border-radius: 8rpx;
}
swiper.square-dot .wx-swiper-dot,
swiper.square-dot .a-swiper-dot,
swiper.square-dot .uni-swiper-dot {
  background-color: #ffffff;
  opacity: 0.4;
  width: 10rpx;
  height: 10rpx;
  border-radius: 20rpx;
  margin: 0 8rpx !important;
}
swiper.square-dot .wx-swiper-dot.wx-swiper-dot-active,
swiper.square-dot .a-swiper-dot.a-swiper-dot-active,
swiper.square-dot .uni-swiper-dot.uni-swiper-dot-active {
  opacity: 1;
  width: 30rpx;
}
swiper.round-dot .wx-swiper-dot,
swiper.round-dot .a-swiper-dot,
swiper.round-dot .uni-swiper-dot {
  width: 10rpx;
  height: 10rpx;
  position: relative;
  margin: 4rpx 8rpx !important;
}
swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active::after,
swiper.round-dot .a-swiper-dot.a-swiper-dot-active::after,
swiper.round-dot .uni-swiper-dot.uni-swiper-dot-active::after {
  content: "";
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  top: 0rpx;
  left: 0rpx;
  right: 0;
  bottom: 0;
  margin: auto;
  background-color: #ffffff;
  border-radius: 20rpx;
}
swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active,
swiper.round-dot .a-swiper-dot.a-swiper-dot-active,
swiper.round-dot .uni-swiper-dot.uni-swiper-dot-active {
  width: 18rpx;
  height: 18rpx;
}
.swiper {
  width: 100%;
}
.swiper image,
.swiper video,
.swiper-item image,
.swiper-item video {
  width: 100%;
  display: block;
  height: 100%;
  margin: 0;
  pointer-events: none;
}
.diygw-swiper-item-title {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 30px;
  line-height: 30px;
  background: rgba(0, 0, 0, 0.281);
  text-align: left;
  font-size: 28rpx;
}
.diygw-swiper-item-title.not-mask {
  position: relative;
  background: inherit;
  line-height: 80rpx;
  padding-left: 5rpx;
}
.diygw-swiper-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;
}
.diygw-swiper-item-wrap {
  width: 100%;
  height: 100%;
  flex: 1;
  transition: all 0.5s;
  overflow: hidden;
  box-sizing: content-box;
  position: relative;
}
.diygw-swiper-item-wrap image, .diygw-swiper-item-wrap video, .diygw-swiper-item-wrap view, .diygw-swiper-item-wrap text, .diygw-swiper-item-wrap button {
  pointer-events: auto !important;
}
.diygw-swiper-image {
  width: 100%;
  height: 100%;
  will-change: transform;
  display: block;
}
.indicator-left-top [class*=-swiper-dots] {
  left: 80rpx;
  top: 20rpx;
  bottom: inherit;
}
.indicator-left-center [class*=-swiper-dots] {
  width: 100% !important;
  left: 0;
  transform: inherit !important;
  top: 20rpx;
  bottom: inherit;
  justify-content: center;
  position: absolute;
  display: flex;
  padding-right: 50rpx;
  flex-direction: row;
  z-index: 1;
}
.indicator-right-top [class*=-swiper-dots] {
  width: calc(100% - 60rpx) !important;
  left: 0;
  transform: inherit !important;
  top: 20rpx;
  bottom: inherit;
  justify-content: flex-end;
  position: absolute;
  display: flex;
  padding-right: 50rpx;
  flex-direction: row;
  z-index: 1;
}
.indicator-left-bottom [class*=-swiper-dots] {
  left: 80rpx;
}
.indicator-right-bottom [class*=-swiper-dots] {
  width: calc(100% - 60rpx) !important;
  left: 0;
  transform: inherit !important;
  justify-content: flex-end;
  position: absolute;
  display: flex;
  padding-right: 50rpx;
  flex-direction: row;
  z-index: 1;
}
/* ==================
          头像
 ==================== */
.diygw-avatar {
  font-variant: small-caps;
  display: inline-flex;
  text-align: center;
  justify-content: center;
  align-items: center;
  background-color: #ccc;
  color: var(--white);
  white-space: nowrap;
  position: relative;
  width: 96rpx;
  height: 96rpx;
  background-size: cover;
  background-position: center;
  vertical-align: middle;
  font-size: 32rpx;
}
.diygw-avatar.diygw-avatar-button {
  padding: 0;
  margin-left: auto;
  margin-right: auto;
}
.diygw-avatar [class*=diy-icon-] {
  font-size: 56rpx;
}
.diygw-avatar.bg-none {
  background-color: inherit;
}
.diygw-avatar .diygw-avatar-img {
  width: 100%;
  height: 100%;
}
.diygw-avatar.none {
  width: 64rpx;
  height: 64rpx;
  font-size: 32rpx;
}
.diygw-avatar.none [class*=diy-icon-] {
  font-size: 32rpx;
}
.diygw-avatar.xs {
  width: 48rpx;
  height: 48rpx;
  font-size: 24rpx;
}
.diygw-avatar.xs [class*=diy-icon-] {
  font-size: 24rpx;
}
.diygw-avatar.sm {
  width: 64rpx;
  height: 64rpx;
  font-size: 32rpx;
}
.diygw-avatar.sm [class*=diy-icon-] {
  font-size: 32rpx;
}
.diygw-avatar.md {
  width: 96rpx;
  height: 96rpx;
  font-size: 48rpx;
}
.diygw-avatar.md [class*=diy-icon-] {
  font-size: 48rpx;
}
.diygw-avatar.lg {
  width: 128rpx;
  height: 128rpx;
  font-size: 72rpx;
}
.diygw-avatar.lg [class*=diy-icon-] {
  font-size: 72rpx;
}
.diygw-avatar.xl {
  width: 200rpx;
  height: 200rpx;
  font-size: 100rpx;
}
.diygw-avatar.xl [class*=diy-icon-] {
  font-size: 100rpx;
}
.diygw-avatar .avatar-text {
  font-size: 0.4em;
}
.diygw-avatar-group {
  unicode-bidi: bidi-override;
  padding: 0 10rpx 0 20rpx;
  display: inline-block;
}
.diygw-avatar-group.rtl {
  direction: rtl;
  padding: 0rpx;
}
.diygw-avatar-group .diygw-avatar {
  margin-left: -30rpx;
  vertical-align: middle;
}
.diygw-avatar-group .diygw-avatar.xs {
  margin-left: -16rpx;
}
.diygw-avatar-group .diygw-avatar.sm {
  margin-left: -20rpx;
}
/* ==================
          背景
 ==================== */
.bg-gradual-red, .gradual-red {
  background-image: var(--redGradual) !important;
  color: var(--white) !important;
}
.bg-gradual-orange, .gradual-orange {
  background-image: var(--orangeGradual) !important;
  color: var(--white) !important;
}
.bg-gradual-green, .gradual-green {
  background-image: var(--greenGradual) !important;
  color: var(--white) !important;
}
.bg-gradual-purple, .gradual-purple {
  background-image: var(--purpleGradual) !important;
  color: var(--white) !important;
}
.bg-gradual-pink, .gradual-pink {
  background-image: var(--pinkGradual) !important;
  color: var(--white) !important;
}
.bg-gradual-blue, .gradual-blue {
  background-image: var(--blueGradual) !important;
  color: var(--white) !important;
}
.bg-red, .red {
  background-color: var(--red) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-red] {
  box-shadow: var(--ShadowSize) var(--redShadow);
}
.bg-red.light, .red.light {
  color: var(--red);
  background-color: var(--redLight);
}
.bg-orange, .orange {
  background-color: var(--orange) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-orange] {
  box-shadow: var(--ShadowSize) var(--orangeShadow);
}
.bg-orange.light, .orange.light {
  color: var(--orange);
  background-color: var(--orangeLight);
}
.bg-yellow, .yellow {
  background-color: var(--yellow) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-yellow] {
  box-shadow: var(--ShadowSize) var(--yellowShadow);
}
.bg-yellow.light, .yellow.light {
  color: var(--yellow);
  background-color: var(--yellowLight);
}
.bg-olive, .olive {
  background-color: var(--olive) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-olive] {
  box-shadow: var(--ShadowSize) var(--oliveShadow);
}
.bg-olive.light, .olive.light {
  color: var(--olive);
  background-color: var(--oliveLight);
}
.bg-green, .green {
  background-color: var(--green) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-green] {
  box-shadow: var(--ShadowSize) var(--greenShadow);
}
.bg-green.light, .green.light {
  color: var(--green);
  background-color: var(--greenLight);
}
.bg-cyan, .cyan {
  background-color: var(--cyan) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-cyan] {
  box-shadow: var(--ShadowSize) var(--cyanShadow);
}
.bg-cyan.light, .cyan.light {
  color: var(--cyan);
  background-color: var(--cyanLight);
}
.bg-blue, .blue {
  background-color: var(--blue) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-blue] {
  box-shadow: var(--ShadowSize) var(--blueShadow);
}
.bg-blue.light, .blue.light {
  color: var(--blue);
  background-color: var(--blueLight);
}
.bg-purple, .purple {
  background-color: var(--purple) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-purple] {
  box-shadow: var(--ShadowSize) var(--purpleShadow);
}
.bg-purple.light, .purple.light {
  color: var(--purple);
  background-color: var(--purpleLight);
}
.bg-mauve, .mauve {
  background-color: var(--mauve) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-mauve] {
  box-shadow: var(--ShadowSize) var(--mauveShadow);
}
.bg-mauve.light, .mauve.light {
  color: var(--mauve);
  background-color: var(--mauveLight);
}
.bg-pink, .pink {
  background-color: var(--pink) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-pink] {
  box-shadow: var(--ShadowSize) var(--pinkShadow);
}
.bg-pink.light, .pink.light {
  color: var(--pink);
  background-color: var(--pinkLight);
}
.bg-brown, .brown {
  background-color: var(--brown) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-brown] {
  box-shadow: var(--ShadowSize) var(--brownShadow);
}
.bg-brown.light, .brown.light {
  color: var(--brown);
  background-color: var(--brownLight);
}
.bg-grey, .grey {
  background-color: var(--grey) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-grey] {
  box-shadow: var(--ShadowSize) var(--greyShadow);
}
.bg-grey.light, .grey.light {
  color: var(--grey);
  background-color: var(--greyLight);
}
.bg-black, .black {
  background-color: var(--black) !important;
  color: var(--white) !important;
}
.diygw-shadow[class*=-black] {
  box-shadow: var(--ShadowSize) var(--blackShadow);
}
.bg-black.light, .black.light {
  color: var(--black);
  background-color: var(--blackLight);
}
.bg-white, .white {
  background-color: var(--white) !important;
  color: var(--black) !important;
}
.diygw-shadow[class*=-white] {
  box-shadow: var(--ShadowSize) var(--whiteShadow);
}
.bg-white.light, .white.light {
  color: var(--white);
  background-color: var(--whiteLight);
}
[class*=diygw-line] {
  background-color: transparent !important;
}
[class*=diygw-line]::after {
  content: " ";
  display: block;
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1rpx solid currentColor;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: 6px;
  z-index: 1;
  pointer-events: none;
}
[class*=diygw-lines]::after {
  border: 6rpx solid currentColor;
}
.diygw-pzx {
  width: 100%;
  margin: 10rpx 0;
}
.bg-shadeTop {
  background-image: linear-gradient(black, rgba(0, 0, 0, 0.01));
  color: var(--white);
}
.bg-shadeBottom {
  background-image: linear-gradient(rgba(0, 0, 0, 0.01), black);
  color: var(--white);
}
.bg-img {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.bg-mask {
  background-color: var(--black);
  position: relative;
}
.bg-mask::after {
  content: "";
  border-radius: inherit;
  width: 100%;
  height: 100%;
  display: block;
  background-color: rgba(0, 0, 0, 0.4);
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
}
.bg-mask view,
.bg-mask cover-view {
  z-index: 5;
  position: relative;
}
.bg-video {
  position: relative;
}
.bg-video video {
  display: block;
  height: 100%;
  width: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  z-index: 0;
  pointer-events: none;
}
/* ==================
         标题栏
 ==================== */
.diygw-title {
  display: flex;
  position: relative;
  align-items: center;
  min-height: 90rpx;
  flex: 1;
  padding: 0 20rpx;
  justify-content: space-between;
}
.diygw-title [class*=diy-icon-] {
  font-size: inherit;
}
.diygw-title .title {
  font-size: 36rpx;
}
.diygw-title .title-bar-icon {
  font-size: 36rpx;
}
.diygw-title .back-text {
  font-size: 24rpx;
}
.diygw-title .more {
  font-size: 24rpx;
}
.diy-navbar .diygw-title {
  padding-right: 200rpx;
}
.diygw-floatbar, .right-top, .right-bottom, .left-top, .left-bottom {
  position: fixed;
  z-index: 9999;
  white-space: nowrap;
  align-items: center;
  display: flex;
  justify-content: space-around;
}
.diygw-floatbar .diygw-grid.diygw-actions, .right-top .diygw-grid.diygw-actions, .right-bottom .diygw-grid.diygw-actions, .left-top .diygw-grid.diygw-actions, .left-bottom .diygw-grid.diygw-actions {
  flex-direction: column;
}
.diygw-floatbar .diygw-grid.diygw-actions .diygw-action, .right-top .diygw-grid.diygw-actions .diygw-action, .right-bottom .diygw-grid.diygw-actions .diygw-action, .left-top .diygw-grid.diygw-actions .diygw-action, .left-bottom .diygw-grid.diygw-actions .diygw-action {
  padding: 10rpx;
}
.diygw-floatbar.inline .diygw-grid-inner, .right-top.inline .diygw-grid-inner, .right-bottom.inline .diygw-grid-inner, .left-top.inline .diygw-grid-inner, .left-bottom.inline .diygw-grid-inner {
  flex-direction: row !important;
}
.diygw-floatbar.inline .diygw-grid-inner .diygw-grid-title, .right-top.inline .diygw-grid-inner .diygw-grid-title, .right-bottom.inline .diygw-grid-inner .diygw-grid-title, .left-top.inline .diygw-grid-inner .diygw-grid-title, .left-bottom.inline .diygw-grid-inner .diygw-grid-title {
  margin-left: 10rpx;
}
.right-top, .right-bottom {
  right: 0px;
}
.left-top, .left-bottom {
  left: 0px;
}
.left-top {
  right: 0px;
}
/* ==================
         模态窗口
 ==================== */
.diygw-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1110;
  opacity: 0;
  outline: 0;
  text-align: center;
  transform: scale(1.185);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  perspective: 1000px;
  background: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease-in-out 0s;
  pointer-events: none;
}
.diygw-modal .diygw-bar .content {
  width: calc(100% - 80rpx);
}
.diygw-modal .diygw-dialog-content {
  float: none !important;
  text-align: left;
  flex-wrap: wrap;
}
.basic .basis-xs {
  width: 20%;
}
.basic .basis-sm {
  width: 40%;
}
.basic .basis-df {
  width: 50%;
}
.basic .basis-lg {
  width: 85%;
}
.basic .basis-xl {
  width: 80%;
}
.diygw-modal::before {
  content: "​";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.diygw-modal.show {
  opacity: 1;
  transition-duration: 0.3s;
  transform: scale(1);
  overflow-x: hidden;
  overflow-y: auto;
  pointer-events: auto;
}
.diygw-dialog {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-left: auto;
  margin-right: auto;
  width: 340px;
  max-width: 100%;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  overflow: hidden;
}
.diygw-dialog.bg-none {
  background-color: rgba(0, 0, 0, 0) !important;
}
.diygw-modal.bottom-modal::before {
  vertical-align: bottom;
}
.diygw-modal.bottom-modal .diygw-dialog {
  width: 100%;
  border-radius: 0;
}
.diygw-modal.bottom-modal {
  margin-bottom: -500px;
}
.diygw-modal.bottom-modal.show {
  margin-bottom: 0;
}
.diygw-modal.drawer-left-modal, .diygw-modal.drawer-right-modal {
  transform: scale(1);
  display: flex;
}
.diygw-modal.drawer-right-modal {
  justify-content: flex-end;
}
.diygw-modal.drawer-left-modal .diygw-dialog, .diygw-modal.drawer-right-modal .diygw-dialog {
  height: 100%;
  min-width: 100px;
  border-radius: 0;
  margin: initial;
  transition-duration: 0.3s;
}
.diygw-modal.drawer-left-modal .diygw-dialog {
  transform: translateX(-100%);
}
.diygw-modal.drawer-right-modal .diygw-dialog {
  transform: translateX(100%);
}
.diygw-modal.drawer-right-modal.show .diygw-dialog, .diygw-modal.drawer-left-modal.show .diygw-dialog {
  transform: translateX(0%);
}
.diygw-modal .diygw-dialog > .diygw-bar:first-child .action {
  min-width: 80rpx;
  margin-right: 0;
  min-height: 50rpx;
}
/* ==================
          列表
 ==================== */
.diygw-list {
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
}
.diygw-list.scroll-view {
  overflow-x: auto;
  flex-wrap: nowrap !important;
  flex-direction: row !important;
}
.diygw-list.scroll-view .uni-scroll-view {
  width: 100%;
}
.diygw-list.scroll-view .diygw-item {
  flex-shrink: 0;
}
.diygw-list.scroll-view .diygw-item.solid-bottom::after {
  border-right: 1px solid rgba(0, 0, 0, 0.1) !important;
}
.diygw-list.not-remark .content {
  flex-direction: row !important;
  align-items: center;
}
.diygw-list .diygw-item {
  position: relative;
  display: flex;
  padding: 20rpx 20rpx;
  justify-content: flex-end;
  align-content: center;
  align-items: stretch;
}
.diygw-list .diygw-item .diygw-avatar {
  flex-shrink: 0;
  font-size: 96rpx;
}
.diygw-list .diygw-item.col-100 {
  width: 100%;
}
.diygw-list .diygw-item.col-100.diygw-card {
  width: calc(100% - 32rpx);
}
.diygw-list .diygw-item.col-50 {
  width: 50%;
}
.diygw-list .diygw-item.col-50.diygw-card {
  width: calc(50% - 32rpx);
}
.diygw-list .diygw-item.col-50.solid-bottom:nth-child(2n+1)::after {
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}
.diygw-list .diygw-item.col-33 {
  width: 33.33%;
}
.diygw-list .diygw-item.col-33.diygw-card {
  width: calc(33.33% - 32rpx);
}
.diygw-list .diygw-item.col-33.solid-bottom:nth-child(3n+1)::after {
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}
.diygw-list .diygw-item.col-33.solid-bottom:nth-child(3n+2)::after {
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}
.diygw-list .diygw-item.col-25 {
  width: 25%;
}
.diygw-list .diygw-item.col-25.diygw-card {
  width: calc(25% - 32rpx);
}
.diygw-list .diygw-item.col-25.solid-bottom:nth-child(4n+1)::after {
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}
.diygw-list .diygw-item.col-25.solid-bottom:nth-child(4n+2)::after {
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}
.diygw-list .diygw-item.col-25.solid-bottom:nth-child(4n+3)::after {
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}
.diygw-list .diygw-item.arrow {
  padding-right: 80rpx !important;
}
.diygw-list .diygw-item.arrow:before {
  position: absolute;
  top: 0;
  right: 30rpx;
  bottom: 0;
  display: block;
  margin: auto;
  width: 30rpx;
  height: 30rpx;
  color: #8799a3;
  content: "\e71a";
  text-align: center;
  font-size: 17px;
  font-family: diygwui;
  line-height: 30rpx;
}
.diygw-list .diygw-item.arrow[class*=bg-]:before {
  color: #fff;
}
.diygw-list .diygw-item .content {
  flex: 1;
  padding: 4rpx 0 4rpx 12rpx;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}
.diygw-list .diygw-item .content .title {
  font-size: 28rpx;
}
.diygw-list .diygw-item .content .remark {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  word-break: break-all;
  word-wrap: break-word;
}
.diygw-list .diygw-item.flex-direction-column-reverse, .diygw-list .diygw-item.flex-direction-column {
  align-items: center;
}
.diygw-list .diygw-item.flex-direction-column-reverse .content, .diygw-list .diygw-item.flex-direction-column .content {
  padding: 4rpx 0;
  text-align: center;
}
.diygw-list .diygw-avatar {
  background-color: inherit;
  color: #333;
}
.diygw-list.small .diygw-avatar {
  width: 48rpx !important;
  height: 48rpx !important;
  font-size: 48rpx !important;
}
.diygw-list.small .content {
  flex-direction: row !important;
  align-items: center;
}
.diygw-list .solid-small-bottom::after {
  width: calc(200% - 80rpx);
  left: 20rpx;
}
.diygw-list [class*=bg-] .diygw-avatar {
  color: #fff;
}
.diygw-list .uni-scroll-view {
  width: 100%;
}
.diygw-list.scroll-view, .diygw-list .uni-scroll-view-content {
  flex-wrap: nowrap !important;
  flex-direction: row !important;
}
.diygw-list.scroll-view::-webkit-scrollbar, .diygw-list .uni-scroll-view-content::-webkit-scrollbar {
  display: none;
}
.diygw-list.scroll-view .diygw-grid-item, .diygw-list .uni-scroll-view-content .diygw-grid-item {
  flex-shrink: 0;
}
/* ==================
         时间轴
 ==================== */
.diygw-timeline {
  display: block;
  background-color: var(--white);
  width: 100%;
}
.diygw-timeline .diygw-time {
  width: 100%;
  text-align: left;
  padding: 20rpx 0;
  font-size: 26rpx;
  font-weight: bold;
  color: #888;
  display: block;
}
.diygw-timeline > .diygw-item {
  padding: 30rpx 30rpx 30rpx 60px;
  position: relative;
  display: block;
  z-index: 0;
  font-size: 12px;
}
.diygw-timeline > .diygw-item:not([class*=diy-icon-])::before {
  content: "\e763";
}
.diygw-timeline > .diygw-item::after {
  content: "";
  display: block;
  position: absolute;
  width: 1px;
  background-color: #ddd;
  left: 60rpx;
  height: 100%;
  top: 0;
  z-index: 8;
}
.diygw-timeline > .diygw-item::before {
  font-size: 36rpx;
  display: block;
  position: absolute;
  top: 36rpx;
  z-index: 9;
  background-color: var(--white);
  width: 50rpx;
  height: 50rpx;
  text-align: center;
  border: none;
  line-height: 50rpx;
  left: 36rpx;
}
.diygw-timeline > .diygw-item > .content {
  padding: 30rpx;
  border-radius: 8rpx;
  display: block;
  line-height: 1.6;
}
.diygw-timeline > .diygw-item > .content:not([class*=bg-]) {
  background-color: var(--ghostWhite);
  color: var(--black);
}
.diygw-timeline > .diygw-item > .content + .content {
  margin-top: 20rpx;
}
.diygw-timeline > .diygw-item:not([class*=text-]) {
  color: #ccc;
}
/* ==================
          步骤条
 ==================== */
.diygw-steps {
  display: flex;
  width: 100%;
}
.diygw-steps .diygw-step-item {
  flex: 1;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
}
.diygw-steps .diygw-step-item:not([class*=text-]) {
  color: var(--grey);
}
.diygw-steps .diygw-step-item [class*=diy-icon-] {
  display: block;
  font-size: 24px;
  line-height: 40px;
}
.diygw-steps .diygw-step-item::before, .diygw-steps .diygw-step-item::after {
  content: "";
  display: block;
  position: absolute;
  height: 0px;
  width: calc(100% - 40px);
  border-bottom: 1px solid #ccc;
  left: calc(0px - (100% - 40px) / 2);
  top: 20px;
  z-index: 0;
}
.diygw-steps .diygw-step-item::after {
  border-bottom: 1px solid currentColor;
  width: 0px;
  transition: all 0.3s ease-in-out 0s;
}
.diygw-steps .diygw-step-item:first-child::before, .diygw-steps .diygw-step-item:first-child::after {
  display: none;
}
.diygw-steps .diygw-step-item .num {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  line-height: 30px;
  margin: 10px auto;
  font-size: 24px;
  border: 1px solid currentColor;
  position: relative;
  overflow: hidden;
}
.diygw-steps .diygw-step-item.small .num {
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
}
.diygw-steps .diygw-step-item.small [class*=diy-icon-] {
  font-size: 16px;
}
.diygw-steps .diygw-step-item.middle .num {
  width: 40px;
  height: 40px;
  line-height: 40px;
}
.diygw-steps .diygw-step-item.middle [class*=diy-icon-] {
  font-size: 30px;
}
.diygw-steps .diygw-step-item.large .num {
  width: 50px;
  height: 50px;
  line-height: 50px;
}
.diygw-steps .diygw-step-item.large [class*=diy-icon-] {
  font-size: 40px;
}
.diygw-steps .diygw-step-item .num::before, .diygw-steps .diygw-step-item .num::after {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  transition: all 0.3s ease-in-out 0s;
  transform: translateY(0px);
}
.diygw-steps .diygw-step-item .num::after {
  content: attr(data-index);
  color: #333;
  transition: all 0.3s ease-in-out 0s;
}
.diygw-steps.steps-arrow .diygw-step-item::before,
.diygw-steps.steps-arrow .diygw-step-item::after {
  content: "\e71a";
  font-family: "diygwui";
  height: 15px;
  border-bottom-width: 0px;
  line-height: 15px;
  top: 0;
  bottom: 0;
  margin: auto;
  color: #ccc;
}
.diygw-steps.steps-arrow .diygw-step-item::after {
  display: none;
}
.diygw-steps.steps-numbers .diygw-step-item::before, .diygw-steps.steps-numbers .diygw-step-item::after {
  top: 25px;
}
.diygw-steps.steps-numbers .diygw-step-item.small::before, .diygw-steps.steps-numbers .diygw-step-item.small::after {
  top: 20px;
}
.diygw-steps.steps-numbers .diygw-step-item.middle::before, .diygw-steps.steps-numbers .diygw-step-item.middle::after {
  top: 30px;
  left: calc(0px - (100% - 50px) / 2);
  width: calc(100% - 50px);
}
.diygw-steps.steps-numbers .diygw-step-item.large::before, .diygw-steps.steps-numbers .diygw-step-item.large::after {
  top: 40px;
  left: calc(0px - (100% - 60px) / 2);
  width: calc(100% - 60px);
}
.diygw-steps.steps-top .diygw-step-item {
  flex-direction: column-reverse;
}
.diygw-steps.steps-top .diygw-step-item::before, .diygw-steps.steps-top .diygw-step-item::after {
  bottom: 20px;
  top: initial;
}
.diygw-steps .diygw-step-item[class*=text-]::after, .diygw-steps .diygw-step-item[class*=text-]::before {
  width: calc(100% - 40px);
  color: currentColor;
}
.diygw-steps .diygw-step-item[class*=text-] .num {
  background-color: currentColor;
}
.diygw-steps .diygw-step-item[class*=text-] .num::before {
  color: var(--white);
  transform: translateY(0px);
}
.diygw-steps .diygw-step-item[class*=text-] .num::after {
  display: none;
}
scroll-view .diygw-steps {
  display: flex;
  white-space: nowrap;
  flex-direction: row;
}
scroll-view .diygw-steps .uni-scroll-view-content {
  flex-direction: row;
  display: flex;
}
scroll-view .diygw-steps .diygw-step-item {
  display: flex;
  min-width: 160rpx;
  flex-shrink: 0;
}
/* ==================
         导航栏
 ==================== */
.diygw-tabs {
  white-space: nowrap;
  width: 100%;
  display: flex;
}
.diygw-tabs.not-border .diygw-tab-item.cur {
  border: 0px !important;
}
.diygw-tabs.not-border .diygw-tab-item.cur::after {
  display: none;
}
.diygw-tabs.not-border[class*=solid-]::after, .diygw-tabs.not-border[class*=solid-]::before {
  display: none;
}
.diygw-tabs .diygw-tab-item {
  height: 90rpx;
  display: inline-block;
  line-height: 90rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
  padding: 0 20rpx;
  position: relative;
}
.diygw-tabs .diygw-tab-item[class*=radius] {
  margin: 10rpx 10rpx;
  height: 70rpx;
  line-height: 70rpx;
}
.diygw-tabs .diygw-tab-item.cur {
  border-bottom: 4rpx solid;
}
.diygw-tabs.scroll-view, .diygw-tabsscroll-view {
  white-space: nowrap;
  overflow-x: auto;
}
.diygw-tabs.scale-title .diygw-tab-item.cur {
  font-size: 36rpx;
}
.diygw-tab-content {
  display: block !important;
}
.flex-direction-column .diygw-tabs.small-border .diygw-tab-item.cur {
  border: 0px;
}
.flex-direction-column .diygw-tabs.small-border .diygw-tab-item.cur::after {
  content: "";
  width: 40rpx;
  height: 4rpx;
  position: absolute;
  background-color: currentColor;
  left: calc(50% - 20rpx);
  bottom: 0;
  margin: auto;
}
.flex-direction-column-reverse .diygw-tab-item.cur {
  border-top: 4rpx solid;
  border-bottom: 0px;
}
.flex-direction-column-reverse .diygw-tabs.small-border .diygw-tab-item.cur {
  border: 0px;
}
.flex-direction-column-reverse .diygw-tabs.small-border .diygw-tab-item.cur::after {
  content: "";
  width: 40rpx;
  height: 4rpx;
  position: absolute;
  background-color: currentColor;
  left: calc(50% - 20rpx);
  top: 0;
  margin: auto;
}
.flex-direction-row .diygw-tabs, .flex-direction-column .flex-direction-row .diygw-tabs {
  flex-direction: column;
  width: 200rpx;
  flex-shrink: 0;
}
.flex-direction-row .diygw-tabs.scroll-view, .flex-direction-row .diygw-tabsscroll-view, .flex-direction-column .flex-direction-row .diygw-tabs.scroll-view, .flex-direction-column .flex-direction-row .diygw-tabsscroll-view {
  white-space: inherit;
  overflow-x: hidden;
  overflow-y: auto;
  flex-direction: column !important;
  justify-content: start !important;
}
.flex-direction-row .diygw-tabs .diygw-tab-item, .flex-direction-column .flex-direction-row .diygw-tabs .diygw-tab-item {
  margin: 0;
  width: 100%;
  display: flex;
  flex-shrink: 0;
  justify-content: center;
}
.flex-direction-row .diygw-tabs .diygw-tab-item.cur, .flex-direction-column .flex-direction-row .diygw-tabs .diygw-tab-item.cur {
  border-bottom: 0px solid;
}
.flex-direction-row .diygw-tabs .diygw-tab-item.cur::after, .flex-direction-column .flex-direction-row .diygw-tabs .diygw-tab-item.cur::after {
  content: "";
  width: 8rpx;
  height: 40rpx;
  border-radius: 0;
  position: absolute;
  background-color: currentColor;
  top: 0;
  left: 0;
  bottom: 0;
  margin: auto;
}
.flex-direction-row-reverse .diygw-tabs, .flex-direction-column .flex-direction-row-reverse .diygw-tabs {
  flex-direction: column;
  width: 200rpx;
  position: relative;
  flex-shrink: 0;
}
.flex-direction-row-reverse .diygw-tabs.scroll-view, .flex-direction-row-reverse .diygw-tabsscroll-view, .flex-direction-column .flex-direction-row-reverse .diygw-tabs.scroll-view, .flex-direction-column .flex-direction-row-reverse .diygw-tabsscroll-view {
  white-space: inherit;
  overflow-x: hidden;
  overflow-y: auto;
  flex-direction: column !important;
  justify-content: start !important;
}
.flex-direction-row-reverse .diygw-tabs .diygw-tab-item, .flex-direction-column .flex-direction-row-reverse .diygw-tabs .diygw-tab-item {
  margin: 0;
  width: 100%;
  display: flex;
  flex-shrink: 0;
  justify-content: center;
}
.flex-direction-row-reverse .diygw-tabs .diygw-tab-item.cur, .flex-direction-column .flex-direction-row-reverse .diygw-tabs .diygw-tab-item.cur {
  border-bottom: 0px solid;
}
.flex-direction-row-reverse .diygw-tabs .diygw-tab-item.cur::after, .flex-direction-column .flex-direction-row-reverse .diygw-tabs .diygw-tab-item.cur::after {
  content: "";
  width: 8rpx;
  height: 30rpx;
  position: absolute;
  background-color: currentColor;
  top: 0;
  left: inherit;
  right: 0;
  bottom: 0;
  margin: auto;
}
/* ==================
         消息组件
 ==================== */
.diy-notice-bar {
  overflow: hidden;
  width: 100%;
}
.diy-direction-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.diy-left-icon {
  align-items: center;
}
.diy-notice-box {
  flex: 1;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  margin-left: 12rpx;
}
.diy-right-icon {
  margin-left: 12rpx;
  display: inline-flex;
  align-items: center;
}
.diy-notice-content {
  animation: diy-loop-animation 10s linear infinite both;
  text-align: right;
  padding-left: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}
.diy-notice-img {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  object-fit: contain;
}
.diy-notice-text {
  font-size: 28rpx;
  word-break: keep-all;
  display: flex;
  flex-direction: row;
  white-space: nowrap;
}
@keyframes diy-loop-animation {
0% {
    transform: translate3d(0, 0, 0);
}
100% {
    transform: translate3d(-100%, 0, 0);
}
}
.diy-notice-swiper {
  height: 40rpx;
}
.diy-notice-swiper {
  height: 40rpx;
  align-items: center;
  justify-content: center;
}
.diy-notice-item {
  white-space: nowrap;
}
.diy-notice-item + .diy-notice-item {
  margin-left: 20rpx;
}
.diy-notice-swiper-item {
  display: block;
  height: 40rpx;
  width: 100%;
  line-height: 40rpx;
  text-align: left;
}
/* ==================
         进度条
 ==================== */
@keyframes progress-stripes {
from {
    background-position: 72rpx 0;
}
to {
    background-position: 0 0;
}
}
.diygw-progress {
  overflow: hidden;
  height: 28rpx;
  background-color: #ebeef5;
  display: inline-flex;
  align-items: center;
  width: 100%;
}
.diygw-progress .diygw-progress-content {
  line-height: 1;
  width: 0;
  height: 100%;
  align-items: center;
  display: flex;
  justify-items: flex-end;
  justify-content: space-around;
  font-size: 10px;
  color: var(--white);
  transition: width 0.6s ease;
}
.diygw-progress.striped .diygw-progress-content {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 72rpx 72rpx;
}
.diygw-progress.striped.active .diygw-progress-content {
  animation: progress-stripes 2s linear infinite;
}
.load-progress {
  pointer-events: none;
  top: 0;
  position: fixed;
  width: 100%;
  left: 0;
  z-index: 2000;
}
.load-progress.hide {
  display: none;
}
.load-progress .load-progress-bar {
  position: relative;
  width: 100%;
  height: 4rpx;
  overflow: hidden;
  transition: all 200ms ease 0s;
}
.load-progress .load-progress-spinner {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  z-index: 2000;
  display: block;
}
.load-progress .load-progress-spinner::after {
  content: "";
  display: block;
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: solid 4rpx transparent;
  border-top-color: inherit;
  border-left-color: inherit;
  border-radius: 50%;
  animation: load-progress-spinner 0.4s linear infinite;
}
@keyframes load-progress-spinner {
0% {
    transform: rotate(0);
}
100% {
    transform: rotate(360deg);
}
}
/* ==================
        折叠面板
==================== */
.diy-collapse-icon {
  transition: transform 0.6s;
}
.diy-collapse-active {
  transform: rotateZ(-180deg);
}
/* ==================
         魔方布局
 ==================== */
.diygw-cubes {
  width: 100vw !important;
  position: relative;
  height: 100vw;
  display: flex;
  align-items: center;
  justify-content: space-around;
  overflow: hidden;
}
.diygw-cubes .diygw-cube-item {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: space-around;
  overflow: hidden;
}
.diygw-cubes .diygw-cube-item .diygw-cube-img {
  object-fit: cover;
  height: 100%;
  width: 100%;
}
/* ==================
         搜索条
 ==================== */
.diygw-search {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 10rpx;
  box-sizing: border-box;
  width: 100%;
  min-width: 0;
  color: var(--black);
  line-height: 48rpx;
  text-align: left;
  background-color: transparent;
  resize: none;
  position: relative;
}
.diygw-search .flex .diygw-tag, .diygw-search .flex [class*=diy-icon-] {
  margin-right: 10rpx;
  height: 48rpx;
}
.diygw-search .flex1 {
  background: transparent;
}
.diygw-search .icon-right {
  width: 50rpx;
}
.diygw-search .diygw-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 10rpx;
}
.diygw-search .right-icon {
  width: 64rpx;
  height: 64rpx;
}
.search.flex {
  padding-right: 0px;
  display: flex;
}
.search.flex .flex1 {
  width: 100%;
  background: transparent;
}
.diy-sticky-100 {
  width: 100% !important;
  background-color: #fff;
}
.diy-sticky-100.diygw-absolute, .diy-sticky-100 .diygw-top, .diy-sticky-100 .diygw-bottom, .diy-sticky-100 .left-bottom, .diy-sticky-100 .left-top, .diy-sticky-100 .right-bottom, .diy-sticky-100 .right-top {
  position: static !important;
}
.u-relative,
.u-rela {
  position: relative;
}
.u-absolute,
.u-abso {
  position: absolute;
}
image {
  display: inline-block;
}
view,
text {
  box-sizing: border-box;
}
.u-font-xs {
  font-size: 22rpx;
}
.u-font-sm {
  font-size: 26rpx;
}
.u-font-md {
  font-size: 28rpx;
}
.u-font-lg {
  font-size: 30rpx;
}
.u-font-xl {
  font-size: 34rpx;
}
.u-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-flex-wrap {
  flex-wrap: wrap;
}
.u-flex-nowrap {
  flex-wrap: nowrap;
}
.u-col-center {
  align-items: center;
}
.u-col-top {
  align-items: flex-start;
}
.u-col-bottom {
  align-items: flex-end;
}
.u-row-center {
  justify-content: center;
}
.u-row-left {
  justify-content: flex-start;
}
.u-row-right {
  justify-content: flex-end;
}
.u-row-between {
  justify-content: space-between;
}
.u-row-around {
  justify-content: space-around;
}
.u-text-left {
  text-align: left;
}
.u-text-center {
  text-align: center;
}
.u-text-right {
  text-align: right;
}
.u-flex-col {
  display: flex;
  flex-direction: column;
}
.u-flex-0 {
  flex: 0;
}
.u-flex-1 {
  flex: 1;
}
.u-flex-2 {
  flex: 2;
}
.u-flex-3 {
  flex: 3;
}
.u-flex-4 {
  flex: 4;
}
.u-flex-5 {
  flex: 5;
}
.u-flex-6 {
  flex: 6;
}
.u-flex-7 {
  flex: 7;
}
.u-flex-8 {
  flex: 8;
}
.u-flex-9 {
  flex: 9;
}
.u-flex-10 {
  flex: 10;
}
.u-flex-11 {
  flex: 11;
}
.u-flex-12 {
  flex: 12;
}
.u-font-9 {
  font-size: 9px;
}
.u-font-10 {
  font-size: 10px;
}
.u-font-11 {
  font-size: 11px;
}
.u-font-12 {
  font-size: 12px;
}
.u-font-13 {
  font-size: 13px;
}
.u-font-14 {
  font-size: 14px;
}
.u-font-15 {
  font-size: 15px;
}
.u-font-16 {
  font-size: 16px;
}
.u-font-17 {
  font-size: 17px;
}
.u-font-18 {
  font-size: 18px;
}
.u-font-19 {
  font-size: 19px;
}
.u-font-20 {
  font-size: 20rpx;
}
.u-font-21 {
  font-size: 21rpx;
}
.u-font-22 {
  font-size: 22rpx;
}
.u-font-23 {
  font-size: 23rpx;
}
.u-font-24 {
  font-size: 24rpx;
}
.u-font-25 {
  font-size: 25rpx;
}
.u-font-26 {
  font-size: 26rpx;
}
.u-font-27 {
  font-size: 27rpx;
}
.u-font-28 {
  font-size: 28rpx;
}
.u-font-29 {
  font-size: 29rpx;
}
.u-font-30 {
  font-size: 30rpx;
}
.u-font-31 {
  font-size: 31rpx;
}
.u-font-32 {
  font-size: 32rpx;
}
.u-font-33 {
  font-size: 33rpx;
}
.u-font-34 {
  font-size: 34rpx;
}
.u-font-35 {
  font-size: 35rpx;
}
.u-font-36 {
  font-size: 36rpx;
}
.u-font-37 {
  font-size: 37rpx;
}
.u-font-38 {
  font-size: 38rpx;
}
.u-font-39 {
  font-size: 39rpx;
}
.u-font-40 {
  font-size: 40rpx;
}
.u-margin-0, .u-m-0 {
  margin: 0rpx !important;
}
.u-padding-0, .u-p-0 {
  padding: 0rpx !important;
}
.u-m-l-0 {
  margin-left: 0rpx !important;
}
.u-p-l-0 {
  padding-left: 0rpx !important;
}
.u-margin-left-0 {
  margin-left: 0rpx !important;
}
.u-padding-left-0 {
  padding-left: 0rpx !important;
}
.u-m-t-0 {
  margin-top: 0rpx !important;
}
.u-p-t-0 {
  padding-top: 0rpx !important;
}
.u-margin-top-0 {
  margin-top: 0rpx !important;
}
.u-padding-top-0 {
  padding-top: 0rpx !important;
}
.u-m-r-0 {
  margin-right: 0rpx !important;
}
.u-p-r-0 {
  padding-right: 0rpx !important;
}
.u-margin-right-0 {
  margin-right: 0rpx !important;
}
.u-padding-right-0 {
  padding-right: 0rpx !important;
}
.u-m-b-0 {
  margin-bottom: 0rpx !important;
}
.u-p-b-0 {
  padding-bottom: 0rpx !important;
}
.u-margin-bottom-0 {
  margin-bottom: 0rpx !important;
}
.u-padding-bottom-0 {
  padding-bottom: 0rpx !important;
}
.u-margin-2, .u-m-2 {
  margin: 2rpx !important;
}
.u-padding-2, .u-p-2 {
  padding: 2rpx !important;
}
.u-m-l-2 {
  margin-left: 2rpx !important;
}
.u-p-l-2 {
  padding-left: 2rpx !important;
}
.u-margin-left-2 {
  margin-left: 2rpx !important;
}
.u-padding-left-2 {
  padding-left: 2rpx !important;
}
.u-m-t-2 {
  margin-top: 2rpx !important;
}
.u-p-t-2 {
  padding-top: 2rpx !important;
}
.u-margin-top-2 {
  margin-top: 2rpx !important;
}
.u-padding-top-2 {
  padding-top: 2rpx !important;
}
.u-m-r-2 {
  margin-right: 2rpx !important;
}
.u-p-r-2 {
  padding-right: 2rpx !important;
}
.u-margin-right-2 {
  margin-right: 2rpx !important;
}
.u-padding-right-2 {
  padding-right: 2rpx !important;
}
.u-m-b-2 {
  margin-bottom: 2rpx !important;
}
.u-p-b-2 {
  padding-bottom: 2rpx !important;
}
.u-margin-bottom-2 {
  margin-bottom: 2rpx !important;
}
.u-padding-bottom-2 {
  padding-bottom: 2rpx !important;
}
.u-margin-4, .u-m-4 {
  margin: 4rpx !important;
}
.u-padding-4, .u-p-4 {
  padding: 4rpx !important;
}
.u-m-l-4 {
  margin-left: 4rpx !important;
}
.u-p-l-4 {
  padding-left: 4rpx !important;
}
.u-margin-left-4 {
  margin-left: 4rpx !important;
}
.u-padding-left-4 {
  padding-left: 4rpx !important;
}
.u-m-t-4 {
  margin-top: 4rpx !important;
}
.u-p-t-4 {
  padding-top: 4rpx !important;
}
.u-margin-top-4 {
  margin-top: 4rpx !important;
}
.u-padding-top-4 {
  padding-top: 4rpx !important;
}
.u-m-r-4 {
  margin-right: 4rpx !important;
}
.u-p-r-4 {
  padding-right: 4rpx !important;
}
.u-margin-right-4 {
  margin-right: 4rpx !important;
}
.u-padding-right-4 {
  padding-right: 4rpx !important;
}
.u-m-b-4 {
  margin-bottom: 4rpx !important;
}
.u-p-b-4 {
  padding-bottom: 4rpx !important;
}
.u-margin-bottom-4 {
  margin-bottom: 4rpx !important;
}
.u-padding-bottom-4 {
  padding-bottom: 4rpx !important;
}
.u-margin-5, .u-m-5 {
  margin: 5rpx !important;
}
.u-padding-5, .u-p-5 {
  padding: 5rpx !important;
}
.u-m-l-5 {
  margin-left: 5rpx !important;
}
.u-p-l-5 {
  padding-left: 5rpx !important;
}
.u-margin-left-5 {
  margin-left: 5rpx !important;
}
.u-padding-left-5 {
  padding-left: 5rpx !important;
}
.u-m-t-5 {
  margin-top: 5rpx !important;
}
.u-p-t-5 {
  padding-top: 5rpx !important;
}
.u-margin-top-5 {
  margin-top: 5rpx !important;
}
.u-padding-top-5 {
  padding-top: 5rpx !important;
}
.u-m-r-5 {
  margin-right: 5rpx !important;
}
.u-p-r-5 {
  padding-right: 5rpx !important;
}
.u-margin-right-5 {
  margin-right: 5rpx !important;
}
.u-padding-right-5 {
  padding-right: 5rpx !important;
}
.u-m-b-5 {
  margin-bottom: 5rpx !important;
}
.u-p-b-5 {
  padding-bottom: 5rpx !important;
}
.u-margin-bottom-5 {
  margin-bottom: 5rpx !important;
}
.u-padding-bottom-5 {
  padding-bottom: 5rpx !important;
}
.u-margin-6, .u-m-6 {
  margin: 6rpx !important;
}
.u-padding-6, .u-p-6 {
  padding: 6rpx !important;
}
.u-m-l-6 {
  margin-left: 6rpx !important;
}
.u-p-l-6 {
  padding-left: 6rpx !important;
}
.u-margin-left-6 {
  margin-left: 6rpx !important;
}
.u-padding-left-6 {
  padding-left: 6rpx !important;
}
.u-m-t-6 {
  margin-top: 6rpx !important;
}
.u-p-t-6 {
  padding-top: 6rpx !important;
}
.u-margin-top-6 {
  margin-top: 6rpx !important;
}
.u-padding-top-6 {
  padding-top: 6rpx !important;
}
.u-m-r-6 {
  margin-right: 6rpx !important;
}
.u-p-r-6 {
  padding-right: 6rpx !important;
}
.u-margin-right-6 {
  margin-right: 6rpx !important;
}
.u-padding-right-6 {
  padding-right: 6rpx !important;
}
.u-m-b-6 {
  margin-bottom: 6rpx !important;
}
.u-p-b-6 {
  padding-bottom: 6rpx !important;
}
.u-margin-bottom-6 {
  margin-bottom: 6rpx !important;
}
.u-padding-bottom-6 {
  padding-bottom: 6rpx !important;
}
.u-margin-8, .u-m-8 {
  margin: 8rpx !important;
}
.u-padding-8, .u-p-8 {
  padding: 8rpx !important;
}
.u-m-l-8 {
  margin-left: 8rpx !important;
}
.u-p-l-8 {
  padding-left: 8rpx !important;
}
.u-margin-left-8 {
  margin-left: 8rpx !important;
}
.u-padding-left-8 {
  padding-left: 8rpx !important;
}
.u-m-t-8 {
  margin-top: 8rpx !important;
}
.u-p-t-8 {
  padding-top: 8rpx !important;
}
.u-margin-top-8 {
  margin-top: 8rpx !important;
}
.u-padding-top-8 {
  padding-top: 8rpx !important;
}
.u-m-r-8 {
  margin-right: 8rpx !important;
}
.u-p-r-8 {
  padding-right: 8rpx !important;
}
.u-margin-right-8 {
  margin-right: 8rpx !important;
}
.u-padding-right-8 {
  padding-right: 8rpx !important;
}
.u-m-b-8 {
  margin-bottom: 8rpx !important;
}
.u-p-b-8 {
  padding-bottom: 8rpx !important;
}
.u-margin-bottom-8 {
  margin-bottom: 8rpx !important;
}
.u-padding-bottom-8 {
  padding-bottom: 8rpx !important;
}
.u-margin-10, .u-m-10 {
  margin: 10rpx !important;
}
.u-padding-10, .u-p-10 {
  padding: 10rpx !important;
}
.u-m-l-10 {
  margin-left: 10rpx !important;
}
.u-p-l-10 {
  padding-left: 10rpx !important;
}
.u-margin-left-10 {
  margin-left: 10rpx !important;
}
.u-padding-left-10 {
  padding-left: 10rpx !important;
}
.u-m-t-10 {
  margin-top: 10rpx !important;
}
.u-p-t-10 {
  padding-top: 10rpx !important;
}
.u-margin-top-10 {
  margin-top: 10rpx !important;
}
.u-padding-top-10 {
  padding-top: 10rpx !important;
}
.u-m-r-10 {
  margin-right: 10rpx !important;
}
.u-p-r-10 {
  padding-right: 10rpx !important;
}
.u-margin-right-10 {
  margin-right: 10rpx !important;
}
.u-padding-right-10 {
  padding-right: 10rpx !important;
}
.u-m-b-10 {
  margin-bottom: 10rpx !important;
}
.u-p-b-10 {
  padding-bottom: 10rpx !important;
}
.u-margin-bottom-10 {
  margin-bottom: 10rpx !important;
}
.u-padding-bottom-10 {
  padding-bottom: 10rpx !important;
}
.u-margin-12, .u-m-12 {
  margin: 12rpx !important;
}
.u-padding-12, .u-p-12 {
  padding: 12rpx !important;
}
.u-m-l-12 {
  margin-left: 12rpx !important;
}
.u-p-l-12 {
  padding-left: 12rpx !important;
}
.u-margin-left-12 {
  margin-left: 12rpx !important;
}
.u-padding-left-12 {
  padding-left: 12rpx !important;
}
.u-m-t-12 {
  margin-top: 12rpx !important;
}
.u-p-t-12 {
  padding-top: 12rpx !important;
}
.u-margin-top-12 {
  margin-top: 12rpx !important;
}
.u-padding-top-12 {
  padding-top: 12rpx !important;
}
.u-m-r-12 {
  margin-right: 12rpx !important;
}
.u-p-r-12 {
  padding-right: 12rpx !important;
}
.u-margin-right-12 {
  margin-right: 12rpx !important;
}
.u-padding-right-12 {
  padding-right: 12rpx !important;
}
.u-m-b-12 {
  margin-bottom: 12rpx !important;
}
.u-p-b-12 {
  padding-bottom: 12rpx !important;
}
.u-margin-bottom-12 {
  margin-bottom: 12rpx !important;
}
.u-padding-bottom-12 {
  padding-bottom: 12rpx !important;
}
.u-margin-14, .u-m-14 {
  margin: 14rpx !important;
}
.u-padding-14, .u-p-14 {
  padding: 14rpx !important;
}
.u-m-l-14 {
  margin-left: 14rpx !important;
}
.u-p-l-14 {
  padding-left: 14rpx !important;
}
.u-margin-left-14 {
  margin-left: 14rpx !important;
}
.u-padding-left-14 {
  padding-left: 14rpx !important;
}
.u-m-t-14 {
  margin-top: 14rpx !important;
}
.u-p-t-14 {
  padding-top: 14rpx !important;
}
.u-margin-top-14 {
  margin-top: 14rpx !important;
}
.u-padding-top-14 {
  padding-top: 14rpx !important;
}
.u-m-r-14 {
  margin-right: 14rpx !important;
}
.u-p-r-14 {
  padding-right: 14rpx !important;
}
.u-margin-right-14 {
  margin-right: 14rpx !important;
}
.u-padding-right-14 {
  padding-right: 14rpx !important;
}
.u-m-b-14 {
  margin-bottom: 14rpx !important;
}
.u-p-b-14 {
  padding-bottom: 14rpx !important;
}
.u-margin-bottom-14 {
  margin-bottom: 14rpx !important;
}
.u-padding-bottom-14 {
  padding-bottom: 14rpx !important;
}
.u-margin-15, .u-m-15 {
  margin: 15rpx !important;
}
.u-padding-15, .u-p-15 {
  padding: 15rpx !important;
}
.u-m-l-15 {
  margin-left: 15rpx !important;
}
.u-p-l-15 {
  padding-left: 15rpx !important;
}
.u-margin-left-15 {
  margin-left: 15rpx !important;
}
.u-padding-left-15 {
  padding-left: 15rpx !important;
}
.u-m-t-15 {
  margin-top: 15rpx !important;
}
.u-p-t-15 {
  padding-top: 15rpx !important;
}
.u-margin-top-15 {
  margin-top: 15rpx !important;
}
.u-padding-top-15 {
  padding-top: 15rpx !important;
}
.u-m-r-15 {
  margin-right: 15rpx !important;
}
.u-p-r-15 {
  padding-right: 15rpx !important;
}
.u-margin-right-15 {
  margin-right: 15rpx !important;
}
.u-padding-right-15 {
  padding-right: 15rpx !important;
}
.u-m-b-15 {
  margin-bottom: 15rpx !important;
}
.u-p-b-15 {
  padding-bottom: 15rpx !important;
}
.u-margin-bottom-15 {
  margin-bottom: 15rpx !important;
}
.u-padding-bottom-15 {
  padding-bottom: 15rpx !important;
}
.u-margin-16, .u-m-16 {
  margin: 16rpx !important;
}
.u-padding-16, .u-p-16 {
  padding: 16rpx !important;
}
.u-m-l-16 {
  margin-left: 16rpx !important;
}
.u-p-l-16 {
  padding-left: 16rpx !important;
}
.u-margin-left-16 {
  margin-left: 16rpx !important;
}
.u-padding-left-16 {
  padding-left: 16rpx !important;
}
.u-m-t-16 {
  margin-top: 16rpx !important;
}
.u-p-t-16 {
  padding-top: 16rpx !important;
}
.u-margin-top-16 {
  margin-top: 16rpx !important;
}
.u-padding-top-16 {
  padding-top: 16rpx !important;
}
.u-m-r-16 {
  margin-right: 16rpx !important;
}
.u-p-r-16 {
  padding-right: 16rpx !important;
}
.u-margin-right-16 {
  margin-right: 16rpx !important;
}
.u-padding-right-16 {
  padding-right: 16rpx !important;
}
.u-m-b-16 {
  margin-bottom: 16rpx !important;
}
.u-p-b-16 {
  padding-bottom: 16rpx !important;
}
.u-margin-bottom-16 {
  margin-bottom: 16rpx !important;
}
.u-padding-bottom-16 {
  padding-bottom: 16rpx !important;
}
.u-margin-18, .u-m-18 {
  margin: 18rpx !important;
}
.u-padding-18, .u-p-18 {
  padding: 18rpx !important;
}
.u-m-l-18 {
  margin-left: 18rpx !important;
}
.u-p-l-18 {
  padding-left: 18rpx !important;
}
.u-margin-left-18 {
  margin-left: 18rpx !important;
}
.u-padding-left-18 {
  padding-left: 18rpx !important;
}
.u-m-t-18 {
  margin-top: 18rpx !important;
}
.u-p-t-18 {
  padding-top: 18rpx !important;
}
.u-margin-top-18 {
  margin-top: 18rpx !important;
}
.u-padding-top-18 {
  padding-top: 18rpx !important;
}
.u-m-r-18 {
  margin-right: 18rpx !important;
}
.u-p-r-18 {
  padding-right: 18rpx !important;
}
.u-margin-right-18 {
  margin-right: 18rpx !important;
}
.u-padding-right-18 {
  padding-right: 18rpx !important;
}
.u-m-b-18 {
  margin-bottom: 18rpx !important;
}
.u-p-b-18 {
  padding-bottom: 18rpx !important;
}
.u-margin-bottom-18 {
  margin-bottom: 18rpx !important;
}
.u-padding-bottom-18 {
  padding-bottom: 18rpx !important;
}
.u-margin-20, .u-m-20 {
  margin: 20rpx !important;
}
.u-padding-20, .u-p-20 {
  padding: 20rpx !important;
}
.u-m-l-20 {
  margin-left: 20rpx !important;
}
.u-p-l-20 {
  padding-left: 20rpx !important;
}
.u-margin-left-20 {
  margin-left: 20rpx !important;
}
.u-padding-left-20 {
  padding-left: 20rpx !important;
}
.u-m-t-20 {
  margin-top: 20rpx !important;
}
.u-p-t-20 {
  padding-top: 20rpx !important;
}
.u-margin-top-20 {
  margin-top: 20rpx !important;
}
.u-padding-top-20 {
  padding-top: 20rpx !important;
}
.u-m-r-20 {
  margin-right: 20rpx !important;
}
.u-p-r-20 {
  padding-right: 20rpx !important;
}
.u-margin-right-20 {
  margin-right: 20rpx !important;
}
.u-padding-right-20 {
  padding-right: 20rpx !important;
}
.u-m-b-20 {
  margin-bottom: 20rpx !important;
}
.u-p-b-20 {
  padding-bottom: 20rpx !important;
}
.u-margin-bottom-20 {
  margin-bottom: 20rpx !important;
}
.u-padding-bottom-20 {
  padding-bottom: 20rpx !important;
}
.u-margin-22, .u-m-22 {
  margin: 22rpx !important;
}
.u-padding-22, .u-p-22 {
  padding: 22rpx !important;
}
.u-m-l-22 {
  margin-left: 22rpx !important;
}
.u-p-l-22 {
  padding-left: 22rpx !important;
}
.u-margin-left-22 {
  margin-left: 22rpx !important;
}
.u-padding-left-22 {
  padding-left: 22rpx !important;
}
.u-m-t-22 {
  margin-top: 22rpx !important;
}
.u-p-t-22 {
  padding-top: 22rpx !important;
}
.u-margin-top-22 {
  margin-top: 22rpx !important;
}
.u-padding-top-22 {
  padding-top: 22rpx !important;
}
.u-m-r-22 {
  margin-right: 22rpx !important;
}
.u-p-r-22 {
  padding-right: 22rpx !important;
}
.u-margin-right-22 {
  margin-right: 22rpx !important;
}
.u-padding-right-22 {
  padding-right: 22rpx !important;
}
.u-m-b-22 {
  margin-bottom: 22rpx !important;
}
.u-p-b-22 {
  padding-bottom: 22rpx !important;
}
.u-margin-bottom-22 {
  margin-bottom: 22rpx !important;
}
.u-padding-bottom-22 {
  padding-bottom: 22rpx !important;
}
.u-margin-24, .u-m-24 {
  margin: 24rpx !important;
}
.u-padding-24, .u-p-24 {
  padding: 24rpx !important;
}
.u-m-l-24 {
  margin-left: 24rpx !important;
}
.u-p-l-24 {
  padding-left: 24rpx !important;
}
.u-margin-left-24 {
  margin-left: 24rpx !important;
}
.u-padding-left-24 {
  padding-left: 24rpx !important;
}
.u-m-t-24 {
  margin-top: 24rpx !important;
}
.u-p-t-24 {
  padding-top: 24rpx !important;
}
.u-margin-top-24 {
  margin-top: 24rpx !important;
}
.u-padding-top-24 {
  padding-top: 24rpx !important;
}
.u-m-r-24 {
  margin-right: 24rpx !important;
}
.u-p-r-24 {
  padding-right: 24rpx !important;
}
.u-margin-right-24 {
  margin-right: 24rpx !important;
}
.u-padding-right-24 {
  padding-right: 24rpx !important;
}
.u-m-b-24 {
  margin-bottom: 24rpx !important;
}
.u-p-b-24 {
  padding-bottom: 24rpx !important;
}
.u-margin-bottom-24 {
  margin-bottom: 24rpx !important;
}
.u-padding-bottom-24 {
  padding-bottom: 24rpx !important;
}
.u-margin-25, .u-m-25 {
  margin: 25rpx !important;
}
.u-padding-25, .u-p-25 {
  padding: 25rpx !important;
}
.u-m-l-25 {
  margin-left: 25rpx !important;
}
.u-p-l-25 {
  padding-left: 25rpx !important;
}
.u-margin-left-25 {
  margin-left: 25rpx !important;
}
.u-padding-left-25 {
  padding-left: 25rpx !important;
}
.u-m-t-25 {
  margin-top: 25rpx !important;
}
.u-p-t-25 {
  padding-top: 25rpx !important;
}
.u-margin-top-25 {
  margin-top: 25rpx !important;
}
.u-padding-top-25 {
  padding-top: 25rpx !important;
}
.u-m-r-25 {
  margin-right: 25rpx !important;
}
.u-p-r-25 {
  padding-right: 25rpx !important;
}
.u-margin-right-25 {
  margin-right: 25rpx !important;
}
.u-padding-right-25 {
  padding-right: 25rpx !important;
}
.u-m-b-25 {
  margin-bottom: 25rpx !important;
}
.u-p-b-25 {
  padding-bottom: 25rpx !important;
}
.u-margin-bottom-25 {
  margin-bottom: 25rpx !important;
}
.u-padding-bottom-25 {
  padding-bottom: 25rpx !important;
}
.u-margin-26, .u-m-26 {
  margin: 26rpx !important;
}
.u-padding-26, .u-p-26 {
  padding: 26rpx !important;
}
.u-m-l-26 {
  margin-left: 26rpx !important;
}
.u-p-l-26 {
  padding-left: 26rpx !important;
}
.u-margin-left-26 {
  margin-left: 26rpx !important;
}
.u-padding-left-26 {
  padding-left: 26rpx !important;
}
.u-m-t-26 {
  margin-top: 26rpx !important;
}
.u-p-t-26 {
  padding-top: 26rpx !important;
}
.u-margin-top-26 {
  margin-top: 26rpx !important;
}
.u-padding-top-26 {
  padding-top: 26rpx !important;
}
.u-m-r-26 {
  margin-right: 26rpx !important;
}
.u-p-r-26 {
  padding-right: 26rpx !important;
}
.u-margin-right-26 {
  margin-right: 26rpx !important;
}
.u-padding-right-26 {
  padding-right: 26rpx !important;
}
.u-m-b-26 {
  margin-bottom: 26rpx !important;
}
.u-p-b-26 {
  padding-bottom: 26rpx !important;
}
.u-margin-bottom-26 {
  margin-bottom: 26rpx !important;
}
.u-padding-bottom-26 {
  padding-bottom: 26rpx !important;
}
.u-margin-28, .u-m-28 {
  margin: 28rpx !important;
}
.u-padding-28, .u-p-28 {
  padding: 28rpx !important;
}
.u-m-l-28 {
  margin-left: 28rpx !important;
}
.u-p-l-28 {
  padding-left: 28rpx !important;
}
.u-margin-left-28 {
  margin-left: 28rpx !important;
}
.u-padding-left-28 {
  padding-left: 28rpx !important;
}
.u-m-t-28 {
  margin-top: 28rpx !important;
}
.u-p-t-28 {
  padding-top: 28rpx !important;
}
.u-margin-top-28 {
  margin-top: 28rpx !important;
}
.u-padding-top-28 {
  padding-top: 28rpx !important;
}
.u-m-r-28 {
  margin-right: 28rpx !important;
}
.u-p-r-28 {
  padding-right: 28rpx !important;
}
.u-margin-right-28 {
  margin-right: 28rpx !important;
}
.u-padding-right-28 {
  padding-right: 28rpx !important;
}
.u-m-b-28 {
  margin-bottom: 28rpx !important;
}
.u-p-b-28 {
  padding-bottom: 28rpx !important;
}
.u-margin-bottom-28 {
  margin-bottom: 28rpx !important;
}
.u-padding-bottom-28 {
  padding-bottom: 28rpx !important;
}
.u-margin-30, .u-m-30 {
  margin: 30rpx !important;
}
.u-padding-30, .u-p-30 {
  padding: 30rpx !important;
}
.u-m-l-30 {
  margin-left: 30rpx !important;
}
.u-p-l-30 {
  padding-left: 30rpx !important;
}
.u-margin-left-30 {
  margin-left: 30rpx !important;
}
.u-padding-left-30 {
  padding-left: 30rpx !important;
}
.u-m-t-30 {
  margin-top: 30rpx !important;
}
.u-p-t-30 {
  padding-top: 30rpx !important;
}
.u-margin-top-30 {
  margin-top: 30rpx !important;
}
.u-padding-top-30 {
  padding-top: 30rpx !important;
}
.u-m-r-30 {
  margin-right: 30rpx !important;
}
.u-p-r-30 {
  padding-right: 30rpx !important;
}
.u-margin-right-30 {
  margin-right: 30rpx !important;
}
.u-padding-right-30 {
  padding-right: 30rpx !important;
}
.u-m-b-30 {
  margin-bottom: 30rpx !important;
}
.u-p-b-30 {
  padding-bottom: 30rpx !important;
}
.u-margin-bottom-30 {
  margin-bottom: 30rpx !important;
}
.u-padding-bottom-30 {
  padding-bottom: 30rpx !important;
}
.u-margin-32, .u-m-32 {
  margin: 32rpx !important;
}
.u-padding-32, .u-p-32 {
  padding: 32rpx !important;
}
.u-m-l-32 {
  margin-left: 32rpx !important;
}
.u-p-l-32 {
  padding-left: 32rpx !important;
}
.u-margin-left-32 {
  margin-left: 32rpx !important;
}
.u-padding-left-32 {
  padding-left: 32rpx !important;
}
.u-m-t-32 {
  margin-top: 32rpx !important;
}
.u-p-t-32 {
  padding-top: 32rpx !important;
}
.u-margin-top-32 {
  margin-top: 32rpx !important;
}
.u-padding-top-32 {
  padding-top: 32rpx !important;
}
.u-m-r-32 {
  margin-right: 32rpx !important;
}
.u-p-r-32 {
  padding-right: 32rpx !important;
}
.u-margin-right-32 {
  margin-right: 32rpx !important;
}
.u-padding-right-32 {
  padding-right: 32rpx !important;
}
.u-m-b-32 {
  margin-bottom: 32rpx !important;
}
.u-p-b-32 {
  padding-bottom: 32rpx !important;
}
.u-margin-bottom-32 {
  margin-bottom: 32rpx !important;
}
.u-padding-bottom-32 {
  padding-bottom: 32rpx !important;
}
.u-margin-34, .u-m-34 {
  margin: 34rpx !important;
}
.u-padding-34, .u-p-34 {
  padding: 34rpx !important;
}
.u-m-l-34 {
  margin-left: 34rpx !important;
}
.u-p-l-34 {
  padding-left: 34rpx !important;
}
.u-margin-left-34 {
  margin-left: 34rpx !important;
}
.u-padding-left-34 {
  padding-left: 34rpx !important;
}
.u-m-t-34 {
  margin-top: 34rpx !important;
}
.u-p-t-34 {
  padding-top: 34rpx !important;
}
.u-margin-top-34 {
  margin-top: 34rpx !important;
}
.u-padding-top-34 {
  padding-top: 34rpx !important;
}
.u-m-r-34 {
  margin-right: 34rpx !important;
}
.u-p-r-34 {
  padding-right: 34rpx !important;
}
.u-margin-right-34 {
  margin-right: 34rpx !important;
}
.u-padding-right-34 {
  padding-right: 34rpx !important;
}
.u-m-b-34 {
  margin-bottom: 34rpx !important;
}
.u-p-b-34 {
  padding-bottom: 34rpx !important;
}
.u-margin-bottom-34 {
  margin-bottom: 34rpx !important;
}
.u-padding-bottom-34 {
  padding-bottom: 34rpx !important;
}
.u-margin-35, .u-m-35 {
  margin: 35rpx !important;
}
.u-padding-35, .u-p-35 {
  padding: 35rpx !important;
}
.u-m-l-35 {
  margin-left: 35rpx !important;
}
.u-p-l-35 {
  padding-left: 35rpx !important;
}
.u-margin-left-35 {
  margin-left: 35rpx !important;
}
.u-padding-left-35 {
  padding-left: 35rpx !important;
}
.u-m-t-35 {
  margin-top: 35rpx !important;
}
.u-p-t-35 {
  padding-top: 35rpx !important;
}
.u-margin-top-35 {
  margin-top: 35rpx !important;
}
.u-padding-top-35 {
  padding-top: 35rpx !important;
}
.u-m-r-35 {
  margin-right: 35rpx !important;
}
.u-p-r-35 {
  padding-right: 35rpx !important;
}
.u-margin-right-35 {
  margin-right: 35rpx !important;
}
.u-padding-right-35 {
  padding-right: 35rpx !important;
}
.u-m-b-35 {
  margin-bottom: 35rpx !important;
}
.u-p-b-35 {
  padding-bottom: 35rpx !important;
}
.u-margin-bottom-35 {
  margin-bottom: 35rpx !important;
}
.u-padding-bottom-35 {
  padding-bottom: 35rpx !important;
}
.u-margin-36, .u-m-36 {
  margin: 36rpx !important;
}
.u-padding-36, .u-p-36 {
  padding: 36rpx !important;
}
.u-m-l-36 {
  margin-left: 36rpx !important;
}
.u-p-l-36 {
  padding-left: 36rpx !important;
}
.u-margin-left-36 {
  margin-left: 36rpx !important;
}
.u-padding-left-36 {
  padding-left: 36rpx !important;
}
.u-m-t-36 {
  margin-top: 36rpx !important;
}
.u-p-t-36 {
  padding-top: 36rpx !important;
}
.u-margin-top-36 {
  margin-top: 36rpx !important;
}
.u-padding-top-36 {
  padding-top: 36rpx !important;
}
.u-m-r-36 {
  margin-right: 36rpx !important;
}
.u-p-r-36 {
  padding-right: 36rpx !important;
}
.u-margin-right-36 {
  margin-right: 36rpx !important;
}
.u-padding-right-36 {
  padding-right: 36rpx !important;
}
.u-m-b-36 {
  margin-bottom: 36rpx !important;
}
.u-p-b-36 {
  padding-bottom: 36rpx !important;
}
.u-margin-bottom-36 {
  margin-bottom: 36rpx !important;
}
.u-padding-bottom-36 {
  padding-bottom: 36rpx !important;
}
.u-margin-38, .u-m-38 {
  margin: 38rpx !important;
}
.u-padding-38, .u-p-38 {
  padding: 38rpx !important;
}
.u-m-l-38 {
  margin-left: 38rpx !important;
}
.u-p-l-38 {
  padding-left: 38rpx !important;
}
.u-margin-left-38 {
  margin-left: 38rpx !important;
}
.u-padding-left-38 {
  padding-left: 38rpx !important;
}
.u-m-t-38 {
  margin-top: 38rpx !important;
}
.u-p-t-38 {
  padding-top: 38rpx !important;
}
.u-margin-top-38 {
  margin-top: 38rpx !important;
}
.u-padding-top-38 {
  padding-top: 38rpx !important;
}
.u-m-r-38 {
  margin-right: 38rpx !important;
}
.u-p-r-38 {
  padding-right: 38rpx !important;
}
.u-margin-right-38 {
  margin-right: 38rpx !important;
}
.u-padding-right-38 {
  padding-right: 38rpx !important;
}
.u-m-b-38 {
  margin-bottom: 38rpx !important;
}
.u-p-b-38 {
  padding-bottom: 38rpx !important;
}
.u-margin-bottom-38 {
  margin-bottom: 38rpx !important;
}
.u-padding-bottom-38 {
  padding-bottom: 38rpx !important;
}
.u-margin-40, .u-m-40 {
  margin: 40rpx !important;
}
.u-padding-40, .u-p-40 {
  padding: 40rpx !important;
}
.u-m-l-40 {
  margin-left: 40rpx !important;
}
.u-p-l-40 {
  padding-left: 40rpx !important;
}
.u-margin-left-40 {
  margin-left: 40rpx !important;
}
.u-padding-left-40 {
  padding-left: 40rpx !important;
}
.u-m-t-40 {
  margin-top: 40rpx !important;
}
.u-p-t-40 {
  padding-top: 40rpx !important;
}
.u-margin-top-40 {
  margin-top: 40rpx !important;
}
.u-padding-top-40 {
  padding-top: 40rpx !important;
}
.u-m-r-40 {
  margin-right: 40rpx !important;
}
.u-p-r-40 {
  padding-right: 40rpx !important;
}
.u-margin-right-40 {
  margin-right: 40rpx !important;
}
.u-padding-right-40 {
  padding-right: 40rpx !important;
}
.u-m-b-40 {
  margin-bottom: 40rpx !important;
}
.u-p-b-40 {
  padding-bottom: 40rpx !important;
}
.u-margin-bottom-40 {
  margin-bottom: 40rpx !important;
}
.u-padding-bottom-40 {
  padding-bottom: 40rpx !important;
}
.u-margin-42, .u-m-42 {
  margin: 42rpx !important;
}
.u-padding-42, .u-p-42 {
  padding: 42rpx !important;
}
.u-m-l-42 {
  margin-left: 42rpx !important;
}
.u-p-l-42 {
  padding-left: 42rpx !important;
}
.u-margin-left-42 {
  margin-left: 42rpx !important;
}
.u-padding-left-42 {
  padding-left: 42rpx !important;
}
.u-m-t-42 {
  margin-top: 42rpx !important;
}
.u-p-t-42 {
  padding-top: 42rpx !important;
}
.u-margin-top-42 {
  margin-top: 42rpx !important;
}
.u-padding-top-42 {
  padding-top: 42rpx !important;
}
.u-m-r-42 {
  margin-right: 42rpx !important;
}
.u-p-r-42 {
  padding-right: 42rpx !important;
}
.u-margin-right-42 {
  margin-right: 42rpx !important;
}
.u-padding-right-42 {
  padding-right: 42rpx !important;
}
.u-m-b-42 {
  margin-bottom: 42rpx !important;
}
.u-p-b-42 {
  padding-bottom: 42rpx !important;
}
.u-margin-bottom-42 {
  margin-bottom: 42rpx !important;
}
.u-padding-bottom-42 {
  padding-bottom: 42rpx !important;
}
.u-margin-44, .u-m-44 {
  margin: 44rpx !important;
}
.u-padding-44, .u-p-44 {
  padding: 44rpx !important;
}
.u-m-l-44 {
  margin-left: 44rpx !important;
}
.u-p-l-44 {
  padding-left: 44rpx !important;
}
.u-margin-left-44 {
  margin-left: 44rpx !important;
}
.u-padding-left-44 {
  padding-left: 44rpx !important;
}
.u-m-t-44 {
  margin-top: 44rpx !important;
}
.u-p-t-44 {
  padding-top: 44rpx !important;
}
.u-margin-top-44 {
  margin-top: 44rpx !important;
}
.u-padding-top-44 {
  padding-top: 44rpx !important;
}
.u-m-r-44 {
  margin-right: 44rpx !important;
}
.u-p-r-44 {
  padding-right: 44rpx !important;
}
.u-margin-right-44 {
  margin-right: 44rpx !important;
}
.u-padding-right-44 {
  padding-right: 44rpx !important;
}
.u-m-b-44 {
  margin-bottom: 44rpx !important;
}
.u-p-b-44 {
  padding-bottom: 44rpx !important;
}
.u-margin-bottom-44 {
  margin-bottom: 44rpx !important;
}
.u-padding-bottom-44 {
  padding-bottom: 44rpx !important;
}
.u-margin-45, .u-m-45 {
  margin: 45rpx !important;
}
.u-padding-45, .u-p-45 {
  padding: 45rpx !important;
}
.u-m-l-45 {
  margin-left: 45rpx !important;
}
.u-p-l-45 {
  padding-left: 45rpx !important;
}
.u-margin-left-45 {
  margin-left: 45rpx !important;
}
.u-padding-left-45 {
  padding-left: 45rpx !important;
}
.u-m-t-45 {
  margin-top: 45rpx !important;
}
.u-p-t-45 {
  padding-top: 45rpx !important;
}
.u-margin-top-45 {
  margin-top: 45rpx !important;
}
.u-padding-top-45 {
  padding-top: 45rpx !important;
}
.u-m-r-45 {
  margin-right: 45rpx !important;
}
.u-p-r-45 {
  padding-right: 45rpx !important;
}
.u-margin-right-45 {
  margin-right: 45rpx !important;
}
.u-padding-right-45 {
  padding-right: 45rpx !important;
}
.u-m-b-45 {
  margin-bottom: 45rpx !important;
}
.u-p-b-45 {
  padding-bottom: 45rpx !important;
}
.u-margin-bottom-45 {
  margin-bottom: 45rpx !important;
}
.u-padding-bottom-45 {
  padding-bottom: 45rpx !important;
}
.u-margin-46, .u-m-46 {
  margin: 46rpx !important;
}
.u-padding-46, .u-p-46 {
  padding: 46rpx !important;
}
.u-m-l-46 {
  margin-left: 46rpx !important;
}
.u-p-l-46 {
  padding-left: 46rpx !important;
}
.u-margin-left-46 {
  margin-left: 46rpx !important;
}
.u-padding-left-46 {
  padding-left: 46rpx !important;
}
.u-m-t-46 {
  margin-top: 46rpx !important;
}
.u-p-t-46 {
  padding-top: 46rpx !important;
}
.u-margin-top-46 {
  margin-top: 46rpx !important;
}
.u-padding-top-46 {
  padding-top: 46rpx !important;
}
.u-m-r-46 {
  margin-right: 46rpx !important;
}
.u-p-r-46 {
  padding-right: 46rpx !important;
}
.u-margin-right-46 {
  margin-right: 46rpx !important;
}
.u-padding-right-46 {
  padding-right: 46rpx !important;
}
.u-m-b-46 {
  margin-bottom: 46rpx !important;
}
.u-p-b-46 {
  padding-bottom: 46rpx !important;
}
.u-margin-bottom-46 {
  margin-bottom: 46rpx !important;
}
.u-padding-bottom-46 {
  padding-bottom: 46rpx !important;
}
.u-margin-48, .u-m-48 {
  margin: 48rpx !important;
}
.u-padding-48, .u-p-48 {
  padding: 48rpx !important;
}
.u-m-l-48 {
  margin-left: 48rpx !important;
}
.u-p-l-48 {
  padding-left: 48rpx !important;
}
.u-margin-left-48 {
  margin-left: 48rpx !important;
}
.u-padding-left-48 {
  padding-left: 48rpx !important;
}
.u-m-t-48 {
  margin-top: 48rpx !important;
}
.u-p-t-48 {
  padding-top: 48rpx !important;
}
.u-margin-top-48 {
  margin-top: 48rpx !important;
}
.u-padding-top-48 {
  padding-top: 48rpx !important;
}
.u-m-r-48 {
  margin-right: 48rpx !important;
}
.u-p-r-48 {
  padding-right: 48rpx !important;
}
.u-margin-right-48 {
  margin-right: 48rpx !important;
}
.u-padding-right-48 {
  padding-right: 48rpx !important;
}
.u-m-b-48 {
  margin-bottom: 48rpx !important;
}
.u-p-b-48 {
  padding-bottom: 48rpx !important;
}
.u-margin-bottom-48 {
  margin-bottom: 48rpx !important;
}
.u-padding-bottom-48 {
  padding-bottom: 48rpx !important;
}
.u-margin-50, .u-m-50 {
  margin: 50rpx !important;
}
.u-padding-50, .u-p-50 {
  padding: 50rpx !important;
}
.u-m-l-50 {
  margin-left: 50rpx !important;
}
.u-p-l-50 {
  padding-left: 50rpx !important;
}
.u-margin-left-50 {
  margin-left: 50rpx !important;
}
.u-padding-left-50 {
  padding-left: 50rpx !important;
}
.u-m-t-50 {
  margin-top: 50rpx !important;
}
.u-p-t-50 {
  padding-top: 50rpx !important;
}
.u-margin-top-50 {
  margin-top: 50rpx !important;
}
.u-padding-top-50 {
  padding-top: 50rpx !important;
}
.u-m-r-50 {
  margin-right: 50rpx !important;
}
.u-p-r-50 {
  padding-right: 50rpx !important;
}
.u-margin-right-50 {
  margin-right: 50rpx !important;
}
.u-padding-right-50 {
  padding-right: 50rpx !important;
}
.u-m-b-50 {
  margin-bottom: 50rpx !important;
}
.u-p-b-50 {
  padding-bottom: 50rpx !important;
}
.u-margin-bottom-50 {
  margin-bottom: 50rpx !important;
}
.u-padding-bottom-50 {
  padding-bottom: 50rpx !important;
}
.u-margin-52, .u-m-52 {
  margin: 52rpx !important;
}
.u-padding-52, .u-p-52 {
  padding: 52rpx !important;
}
.u-m-l-52 {
  margin-left: 52rpx !important;
}
.u-p-l-52 {
  padding-left: 52rpx !important;
}
.u-margin-left-52 {
  margin-left: 52rpx !important;
}
.u-padding-left-52 {
  padding-left: 52rpx !important;
}
.u-m-t-52 {
  margin-top: 52rpx !important;
}
.u-p-t-52 {
  padding-top: 52rpx !important;
}
.u-margin-top-52 {
  margin-top: 52rpx !important;
}
.u-padding-top-52 {
  padding-top: 52rpx !important;
}
.u-m-r-52 {
  margin-right: 52rpx !important;
}
.u-p-r-52 {
  padding-right: 52rpx !important;
}
.u-margin-right-52 {
  margin-right: 52rpx !important;
}
.u-padding-right-52 {
  padding-right: 52rpx !important;
}
.u-m-b-52 {
  margin-bottom: 52rpx !important;
}
.u-p-b-52 {
  padding-bottom: 52rpx !important;
}
.u-margin-bottom-52 {
  margin-bottom: 52rpx !important;
}
.u-padding-bottom-52 {
  padding-bottom: 52rpx !important;
}
.u-margin-54, .u-m-54 {
  margin: 54rpx !important;
}
.u-padding-54, .u-p-54 {
  padding: 54rpx !important;
}
.u-m-l-54 {
  margin-left: 54rpx !important;
}
.u-p-l-54 {
  padding-left: 54rpx !important;
}
.u-margin-left-54 {
  margin-left: 54rpx !important;
}
.u-padding-left-54 {
  padding-left: 54rpx !important;
}
.u-m-t-54 {
  margin-top: 54rpx !important;
}
.u-p-t-54 {
  padding-top: 54rpx !important;
}
.u-margin-top-54 {
  margin-top: 54rpx !important;
}
.u-padding-top-54 {
  padding-top: 54rpx !important;
}
.u-m-r-54 {
  margin-right: 54rpx !important;
}
.u-p-r-54 {
  padding-right: 54rpx !important;
}
.u-margin-right-54 {
  margin-right: 54rpx !important;
}
.u-padding-right-54 {
  padding-right: 54rpx !important;
}
.u-m-b-54 {
  margin-bottom: 54rpx !important;
}
.u-p-b-54 {
  padding-bottom: 54rpx !important;
}
.u-margin-bottom-54 {
  margin-bottom: 54rpx !important;
}
.u-padding-bottom-54 {
  padding-bottom: 54rpx !important;
}
.u-margin-55, .u-m-55 {
  margin: 55rpx !important;
}
.u-padding-55, .u-p-55 {
  padding: 55rpx !important;
}
.u-m-l-55 {
  margin-left: 55rpx !important;
}
.u-p-l-55 {
  padding-left: 55rpx !important;
}
.u-margin-left-55 {
  margin-left: 55rpx !important;
}
.u-padding-left-55 {
  padding-left: 55rpx !important;
}
.u-m-t-55 {
  margin-top: 55rpx !important;
}
.u-p-t-55 {
  padding-top: 55rpx !important;
}
.u-margin-top-55 {
  margin-top: 55rpx !important;
}
.u-padding-top-55 {
  padding-top: 55rpx !important;
}
.u-m-r-55 {
  margin-right: 55rpx !important;
}
.u-p-r-55 {
  padding-right: 55rpx !important;
}
.u-margin-right-55 {
  margin-right: 55rpx !important;
}
.u-padding-right-55 {
  padding-right: 55rpx !important;
}
.u-m-b-55 {
  margin-bottom: 55rpx !important;
}
.u-p-b-55 {
  padding-bottom: 55rpx !important;
}
.u-margin-bottom-55 {
  margin-bottom: 55rpx !important;
}
.u-padding-bottom-55 {
  padding-bottom: 55rpx !important;
}
.u-margin-56, .u-m-56 {
  margin: 56rpx !important;
}
.u-padding-56, .u-p-56 {
  padding: 56rpx !important;
}
.u-m-l-56 {
  margin-left: 56rpx !important;
}
.u-p-l-56 {
  padding-left: 56rpx !important;
}
.u-margin-left-56 {
  margin-left: 56rpx !important;
}
.u-padding-left-56 {
  padding-left: 56rpx !important;
}
.u-m-t-56 {
  margin-top: 56rpx !important;
}
.u-p-t-56 {
  padding-top: 56rpx !important;
}
.u-margin-top-56 {
  margin-top: 56rpx !important;
}
.u-padding-top-56 {
  padding-top: 56rpx !important;
}
.u-m-r-56 {
  margin-right: 56rpx !important;
}
.u-p-r-56 {
  padding-right: 56rpx !important;
}
.u-margin-right-56 {
  margin-right: 56rpx !important;
}
.u-padding-right-56 {
  padding-right: 56rpx !important;
}
.u-m-b-56 {
  margin-bottom: 56rpx !important;
}
.u-p-b-56 {
  padding-bottom: 56rpx !important;
}
.u-margin-bottom-56 {
  margin-bottom: 56rpx !important;
}
.u-padding-bottom-56 {
  padding-bottom: 56rpx !important;
}
.u-margin-58, .u-m-58 {
  margin: 58rpx !important;
}
.u-padding-58, .u-p-58 {
  padding: 58rpx !important;
}
.u-m-l-58 {
  margin-left: 58rpx !important;
}
.u-p-l-58 {
  padding-left: 58rpx !important;
}
.u-margin-left-58 {
  margin-left: 58rpx !important;
}
.u-padding-left-58 {
  padding-left: 58rpx !important;
}
.u-m-t-58 {
  margin-top: 58rpx !important;
}
.u-p-t-58 {
  padding-top: 58rpx !important;
}
.u-margin-top-58 {
  margin-top: 58rpx !important;
}
.u-padding-top-58 {
  padding-top: 58rpx !important;
}
.u-m-r-58 {
  margin-right: 58rpx !important;
}
.u-p-r-58 {
  padding-right: 58rpx !important;
}
.u-margin-right-58 {
  margin-right: 58rpx !important;
}
.u-padding-right-58 {
  padding-right: 58rpx !important;
}
.u-m-b-58 {
  margin-bottom: 58rpx !important;
}
.u-p-b-58 {
  padding-bottom: 58rpx !important;
}
.u-margin-bottom-58 {
  margin-bottom: 58rpx !important;
}
.u-padding-bottom-58 {
  padding-bottom: 58rpx !important;
}
.u-margin-60, .u-m-60 {
  margin: 60rpx !important;
}
.u-padding-60, .u-p-60 {
  padding: 60rpx !important;
}
.u-m-l-60 {
  margin-left: 60rpx !important;
}
.u-p-l-60 {
  padding-left: 60rpx !important;
}
.u-margin-left-60 {
  margin-left: 60rpx !important;
}
.u-padding-left-60 {
  padding-left: 60rpx !important;
}
.u-m-t-60 {
  margin-top: 60rpx !important;
}
.u-p-t-60 {
  padding-top: 60rpx !important;
}
.u-margin-top-60 {
  margin-top: 60rpx !important;
}
.u-padding-top-60 {
  padding-top: 60rpx !important;
}
.u-m-r-60 {
  margin-right: 60rpx !important;
}
.u-p-r-60 {
  padding-right: 60rpx !important;
}
.u-margin-right-60 {
  margin-right: 60rpx !important;
}
.u-padding-right-60 {
  padding-right: 60rpx !important;
}
.u-m-b-60 {
  margin-bottom: 60rpx !important;
}
.u-p-b-60 {
  padding-bottom: 60rpx !important;
}
.u-margin-bottom-60 {
  margin-bottom: 60rpx !important;
}
.u-padding-bottom-60 {
  padding-bottom: 60rpx !important;
}
.u-margin-62, .u-m-62 {
  margin: 62rpx !important;
}
.u-padding-62, .u-p-62 {
  padding: 62rpx !important;
}
.u-m-l-62 {
  margin-left: 62rpx !important;
}
.u-p-l-62 {
  padding-left: 62rpx !important;
}
.u-margin-left-62 {
  margin-left: 62rpx !important;
}
.u-padding-left-62 {
  padding-left: 62rpx !important;
}
.u-m-t-62 {
  margin-top: 62rpx !important;
}
.u-p-t-62 {
  padding-top: 62rpx !important;
}
.u-margin-top-62 {
  margin-top: 62rpx !important;
}
.u-padding-top-62 {
  padding-top: 62rpx !important;
}
.u-m-r-62 {
  margin-right: 62rpx !important;
}
.u-p-r-62 {
  padding-right: 62rpx !important;
}
.u-margin-right-62 {
  margin-right: 62rpx !important;
}
.u-padding-right-62 {
  padding-right: 62rpx !important;
}
.u-m-b-62 {
  margin-bottom: 62rpx !important;
}
.u-p-b-62 {
  padding-bottom: 62rpx !important;
}
.u-margin-bottom-62 {
  margin-bottom: 62rpx !important;
}
.u-padding-bottom-62 {
  padding-bottom: 62rpx !important;
}
.u-margin-64, .u-m-64 {
  margin: 64rpx !important;
}
.u-padding-64, .u-p-64 {
  padding: 64rpx !important;
}
.u-m-l-64 {
  margin-left: 64rpx !important;
}
.u-p-l-64 {
  padding-left: 64rpx !important;
}
.u-margin-left-64 {
  margin-left: 64rpx !important;
}
.u-padding-left-64 {
  padding-left: 64rpx !important;
}
.u-m-t-64 {
  margin-top: 64rpx !important;
}
.u-p-t-64 {
  padding-top: 64rpx !important;
}
.u-margin-top-64 {
  margin-top: 64rpx !important;
}
.u-padding-top-64 {
  padding-top: 64rpx !important;
}
.u-m-r-64 {
  margin-right: 64rpx !important;
}
.u-p-r-64 {
  padding-right: 64rpx !important;
}
.u-margin-right-64 {
  margin-right: 64rpx !important;
}
.u-padding-right-64 {
  padding-right: 64rpx !important;
}
.u-m-b-64 {
  margin-bottom: 64rpx !important;
}
.u-p-b-64 {
  padding-bottom: 64rpx !important;
}
.u-margin-bottom-64 {
  margin-bottom: 64rpx !important;
}
.u-padding-bottom-64 {
  padding-bottom: 64rpx !important;
}
.u-margin-65, .u-m-65 {
  margin: 65rpx !important;
}
.u-padding-65, .u-p-65 {
  padding: 65rpx !important;
}
.u-m-l-65 {
  margin-left: 65rpx !important;
}
.u-p-l-65 {
  padding-left: 65rpx !important;
}
.u-margin-left-65 {
  margin-left: 65rpx !important;
}
.u-padding-left-65 {
  padding-left: 65rpx !important;
}
.u-m-t-65 {
  margin-top: 65rpx !important;
}
.u-p-t-65 {
  padding-top: 65rpx !important;
}
.u-margin-top-65 {
  margin-top: 65rpx !important;
}
.u-padding-top-65 {
  padding-top: 65rpx !important;
}
.u-m-r-65 {
  margin-right: 65rpx !important;
}
.u-p-r-65 {
  padding-right: 65rpx !important;
}
.u-margin-right-65 {
  margin-right: 65rpx !important;
}
.u-padding-right-65 {
  padding-right: 65rpx !important;
}
.u-m-b-65 {
  margin-bottom: 65rpx !important;
}
.u-p-b-65 {
  padding-bottom: 65rpx !important;
}
.u-margin-bottom-65 {
  margin-bottom: 65rpx !important;
}
.u-padding-bottom-65 {
  padding-bottom: 65rpx !important;
}
.u-margin-66, .u-m-66 {
  margin: 66rpx !important;
}
.u-padding-66, .u-p-66 {
  padding: 66rpx !important;
}
.u-m-l-66 {
  margin-left: 66rpx !important;
}
.u-p-l-66 {
  padding-left: 66rpx !important;
}
.u-margin-left-66 {
  margin-left: 66rpx !important;
}
.u-padding-left-66 {
  padding-left: 66rpx !important;
}
.u-m-t-66 {
  margin-top: 66rpx !important;
}
.u-p-t-66 {
  padding-top: 66rpx !important;
}
.u-margin-top-66 {
  margin-top: 66rpx !important;
}
.u-padding-top-66 {
  padding-top: 66rpx !important;
}
.u-m-r-66 {
  margin-right: 66rpx !important;
}
.u-p-r-66 {
  padding-right: 66rpx !important;
}
.u-margin-right-66 {
  margin-right: 66rpx !important;
}
.u-padding-right-66 {
  padding-right: 66rpx !important;
}
.u-m-b-66 {
  margin-bottom: 66rpx !important;
}
.u-p-b-66 {
  padding-bottom: 66rpx !important;
}
.u-margin-bottom-66 {
  margin-bottom: 66rpx !important;
}
.u-padding-bottom-66 {
  padding-bottom: 66rpx !important;
}
.u-margin-68, .u-m-68 {
  margin: 68rpx !important;
}
.u-padding-68, .u-p-68 {
  padding: 68rpx !important;
}
.u-m-l-68 {
  margin-left: 68rpx !important;
}
.u-p-l-68 {
  padding-left: 68rpx !important;
}
.u-margin-left-68 {
  margin-left: 68rpx !important;
}
.u-padding-left-68 {
  padding-left: 68rpx !important;
}
.u-m-t-68 {
  margin-top: 68rpx !important;
}
.u-p-t-68 {
  padding-top: 68rpx !important;
}
.u-margin-top-68 {
  margin-top: 68rpx !important;
}
.u-padding-top-68 {
  padding-top: 68rpx !important;
}
.u-m-r-68 {
  margin-right: 68rpx !important;
}
.u-p-r-68 {
  padding-right: 68rpx !important;
}
.u-margin-right-68 {
  margin-right: 68rpx !important;
}
.u-padding-right-68 {
  padding-right: 68rpx !important;
}
.u-m-b-68 {
  margin-bottom: 68rpx !important;
}
.u-p-b-68 {
  padding-bottom: 68rpx !important;
}
.u-margin-bottom-68 {
  margin-bottom: 68rpx !important;
}
.u-padding-bottom-68 {
  padding-bottom: 68rpx !important;
}
.u-margin-70, .u-m-70 {
  margin: 70rpx !important;
}
.u-padding-70, .u-p-70 {
  padding: 70rpx !important;
}
.u-m-l-70 {
  margin-left: 70rpx !important;
}
.u-p-l-70 {
  padding-left: 70rpx !important;
}
.u-margin-left-70 {
  margin-left: 70rpx !important;
}
.u-padding-left-70 {
  padding-left: 70rpx !important;
}
.u-m-t-70 {
  margin-top: 70rpx !important;
}
.u-p-t-70 {
  padding-top: 70rpx !important;
}
.u-margin-top-70 {
  margin-top: 70rpx !important;
}
.u-padding-top-70 {
  padding-top: 70rpx !important;
}
.u-m-r-70 {
  margin-right: 70rpx !important;
}
.u-p-r-70 {
  padding-right: 70rpx !important;
}
.u-margin-right-70 {
  margin-right: 70rpx !important;
}
.u-padding-right-70 {
  padding-right: 70rpx !important;
}
.u-m-b-70 {
  margin-bottom: 70rpx !important;
}
.u-p-b-70 {
  padding-bottom: 70rpx !important;
}
.u-margin-bottom-70 {
  margin-bottom: 70rpx !important;
}
.u-padding-bottom-70 {
  padding-bottom: 70rpx !important;
}
.u-margin-72, .u-m-72 {
  margin: 72rpx !important;
}
.u-padding-72, .u-p-72 {
  padding: 72rpx !important;
}
.u-m-l-72 {
  margin-left: 72rpx !important;
}
.u-p-l-72 {
  padding-left: 72rpx !important;
}
.u-margin-left-72 {
  margin-left: 72rpx !important;
}
.u-padding-left-72 {
  padding-left: 72rpx !important;
}
.u-m-t-72 {
  margin-top: 72rpx !important;
}
.u-p-t-72 {
  padding-top: 72rpx !important;
}
.u-margin-top-72 {
  margin-top: 72rpx !important;
}
.u-padding-top-72 {
  padding-top: 72rpx !important;
}
.u-m-r-72 {
  margin-right: 72rpx !important;
}
.u-p-r-72 {
  padding-right: 72rpx !important;
}
.u-margin-right-72 {
  margin-right: 72rpx !important;
}
.u-padding-right-72 {
  padding-right: 72rpx !important;
}
.u-m-b-72 {
  margin-bottom: 72rpx !important;
}
.u-p-b-72 {
  padding-bottom: 72rpx !important;
}
.u-margin-bottom-72 {
  margin-bottom: 72rpx !important;
}
.u-padding-bottom-72 {
  padding-bottom: 72rpx !important;
}
.u-margin-74, .u-m-74 {
  margin: 74rpx !important;
}
.u-padding-74, .u-p-74 {
  padding: 74rpx !important;
}
.u-m-l-74 {
  margin-left: 74rpx !important;
}
.u-p-l-74 {
  padding-left: 74rpx !important;
}
.u-margin-left-74 {
  margin-left: 74rpx !important;
}
.u-padding-left-74 {
  padding-left: 74rpx !important;
}
.u-m-t-74 {
  margin-top: 74rpx !important;
}
.u-p-t-74 {
  padding-top: 74rpx !important;
}
.u-margin-top-74 {
  margin-top: 74rpx !important;
}
.u-padding-top-74 {
  padding-top: 74rpx !important;
}
.u-m-r-74 {
  margin-right: 74rpx !important;
}
.u-p-r-74 {
  padding-right: 74rpx !important;
}
.u-margin-right-74 {
  margin-right: 74rpx !important;
}
.u-padding-right-74 {
  padding-right: 74rpx !important;
}
.u-m-b-74 {
  margin-bottom: 74rpx !important;
}
.u-p-b-74 {
  padding-bottom: 74rpx !important;
}
.u-margin-bottom-74 {
  margin-bottom: 74rpx !important;
}
.u-padding-bottom-74 {
  padding-bottom: 74rpx !important;
}
.u-margin-75, .u-m-75 {
  margin: 75rpx !important;
}
.u-padding-75, .u-p-75 {
  padding: 75rpx !important;
}
.u-m-l-75 {
  margin-left: 75rpx !important;
}
.u-p-l-75 {
  padding-left: 75rpx !important;
}
.u-margin-left-75 {
  margin-left: 75rpx !important;
}
.u-padding-left-75 {
  padding-left: 75rpx !important;
}
.u-m-t-75 {
  margin-top: 75rpx !important;
}
.u-p-t-75 {
  padding-top: 75rpx !important;
}
.u-margin-top-75 {
  margin-top: 75rpx !important;
}
.u-padding-top-75 {
  padding-top: 75rpx !important;
}
.u-m-r-75 {
  margin-right: 75rpx !important;
}
.u-p-r-75 {
  padding-right: 75rpx !important;
}
.u-margin-right-75 {
  margin-right: 75rpx !important;
}
.u-padding-right-75 {
  padding-right: 75rpx !important;
}
.u-m-b-75 {
  margin-bottom: 75rpx !important;
}
.u-p-b-75 {
  padding-bottom: 75rpx !important;
}
.u-margin-bottom-75 {
  margin-bottom: 75rpx !important;
}
.u-padding-bottom-75 {
  padding-bottom: 75rpx !important;
}
.u-margin-76, .u-m-76 {
  margin: 76rpx !important;
}
.u-padding-76, .u-p-76 {
  padding: 76rpx !important;
}
.u-m-l-76 {
  margin-left: 76rpx !important;
}
.u-p-l-76 {
  padding-left: 76rpx !important;
}
.u-margin-left-76 {
  margin-left: 76rpx !important;
}
.u-padding-left-76 {
  padding-left: 76rpx !important;
}
.u-m-t-76 {
  margin-top: 76rpx !important;
}
.u-p-t-76 {
  padding-top: 76rpx !important;
}
.u-margin-top-76 {
  margin-top: 76rpx !important;
}
.u-padding-top-76 {
  padding-top: 76rpx !important;
}
.u-m-r-76 {
  margin-right: 76rpx !important;
}
.u-p-r-76 {
  padding-right: 76rpx !important;
}
.u-margin-right-76 {
  margin-right: 76rpx !important;
}
.u-padding-right-76 {
  padding-right: 76rpx !important;
}
.u-m-b-76 {
  margin-bottom: 76rpx !important;
}
.u-p-b-76 {
  padding-bottom: 76rpx !important;
}
.u-margin-bottom-76 {
  margin-bottom: 76rpx !important;
}
.u-padding-bottom-76 {
  padding-bottom: 76rpx !important;
}
.u-margin-78, .u-m-78 {
  margin: 78rpx !important;
}
.u-padding-78, .u-p-78 {
  padding: 78rpx !important;
}
.u-m-l-78 {
  margin-left: 78rpx !important;
}
.u-p-l-78 {
  padding-left: 78rpx !important;
}
.u-margin-left-78 {
  margin-left: 78rpx !important;
}
.u-padding-left-78 {
  padding-left: 78rpx !important;
}
.u-m-t-78 {
  margin-top: 78rpx !important;
}
.u-p-t-78 {
  padding-top: 78rpx !important;
}
.u-margin-top-78 {
  margin-top: 78rpx !important;
}
.u-padding-top-78 {
  padding-top: 78rpx !important;
}
.u-m-r-78 {
  margin-right: 78rpx !important;
}
.u-p-r-78 {
  padding-right: 78rpx !important;
}
.u-margin-right-78 {
  margin-right: 78rpx !important;
}
.u-padding-right-78 {
  padding-right: 78rpx !important;
}
.u-m-b-78 {
  margin-bottom: 78rpx !important;
}
.u-p-b-78 {
  padding-bottom: 78rpx !important;
}
.u-margin-bottom-78 {
  margin-bottom: 78rpx !important;
}
.u-padding-bottom-78 {
  padding-bottom: 78rpx !important;
}
.u-margin-80, .u-m-80 {
  margin: 80rpx !important;
}
.u-padding-80, .u-p-80 {
  padding: 80rpx !important;
}
.u-m-l-80 {
  margin-left: 80rpx !important;
}
.u-p-l-80 {
  padding-left: 80rpx !important;
}
.u-margin-left-80 {
  margin-left: 80rpx !important;
}
.u-padding-left-80 {
  padding-left: 80rpx !important;
}
.u-m-t-80 {
  margin-top: 80rpx !important;
}
.u-p-t-80 {
  padding-top: 80rpx !important;
}
.u-margin-top-80 {
  margin-top: 80rpx !important;
}
.u-padding-top-80 {
  padding-top: 80rpx !important;
}
.u-m-r-80 {
  margin-right: 80rpx !important;
}
.u-p-r-80 {
  padding-right: 80rpx !important;
}
.u-margin-right-80 {
  margin-right: 80rpx !important;
}
.u-padding-right-80 {
  padding-right: 80rpx !important;
}
.u-m-b-80 {
  margin-bottom: 80rpx !important;
}
.u-p-b-80 {
  padding-bottom: 80rpx !important;
}
.u-margin-bottom-80 {
  margin-bottom: 80rpx !important;
}
.u-padding-bottom-80 {
  padding-bottom: 80rpx !important;
}
.u-reset-nvue {
  flex-direction: row;
  align-items: center;
}
.u-type-primary-light {
  color: #dbf1e1;
}
.u-type-warning-light {
  color: #fdf6ec;
}
.u-type-success-light {
  color: #dbf1e1;
}
.u-type-error-light {
  color: #fef0f0;
}
.u-type-info-light {
  color: #f4f4f5;
}
.u-type-primary-light-bg {
  background-color: #dbf1e1;
}
.u-type-warning-light-bg {
  background-color: #fdf6ec;
}
.u-type-success-light-bg {
  background-color: #dbf1e1;
}
.u-type-error-light-bg {
  background-color: #fef0f0;
}
.u-type-info-light-bg {
  background-color: #f4f4f5;
}
.u-type-primary-dark {
  color: #18b566;
}
.u-type-warning-dark {
  color: #f29100;
}
.u-type-success-dark {
  color: #18b566;
}
.u-type-error-dark {
  color: #dd6161;
}
.u-type-info-dark {
  color: #82848a;
}
.u-type-primary-dark-bg {
  background-color: #18b566;
}
.u-type-warning-dark-bg {
  background-color: #f29100;
}
.u-type-success-dark-bg {
  background-color: #18b566;
}
.u-type-error-dark-bg {
  background-color: #dd6161;
}
.u-type-info-dark-bg {
  background-color: #82848a;
}
.u-type-primary-disabled {
  color: #71d5a1;
}
.u-type-warning-disabled {
  color: #fcbd71;
}
.u-type-success-disabled {
  color: #71d5a1;
}
.u-type-error-disabled {
  color: #fab6b6;
}
.u-type-info-disabled {
  color: #c8c9cc;
}
.u-type-primary {
  color: #19be6b;
}
.u-type-warning {
  color: #ff9900;
}
.u-type-success {
  color: #19be6b;
}
.u-type-error {
  color: #fa3534;
}
.u-type-info {
  color: #909399;
}
.u-type-primary-bg {
  background-color: #19be6b;
}
.u-type-warning-bg {
  background-color: #ff9900;
}
.u-type-success-bg {
  background-color: #19be6b;
}
.u-type-error-bg {
  background-color: #fa3534;
}
.u-type-info-bg {
  background-color: #909399;
}
.u-main-color {
  color: #303133;
}
.u-content-color {
  color: #606266;
}
.u-tips-color {
  color: #909399;
}
.u-light-color {
  color: #c0c4cc;
}
page {
  color: #303133;
  font-size: 24rpx;
}
/* start--去除webkit的默认样式--start */
.u-fix-ios-appearance {
  -webkit-appearance: none;
}
/* end--去除webkit的默认样式--end */
/* start--icon图标外层套一个view，让其达到更好的垂直居中的效果--start */
.u-icon-wrap {
  display: flex;
  align-items: center;
}
/* end-icon图标外层套一个view，让其达到更好的垂直居中的效果--end */
/* start--iPhoneX底部安全区定义--start */
.safe-area-inset-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
/* end-iPhoneX底部安全区定义--end */
/* start--各种hover点击反馈相关的类名-start */
.u-hover-class {
  opacity: 0.6;
}
.u-cell-hover {
  background-color: #f7f8f9 !important;
}
/* end--各种hover点击反馈相关的类名--end */
/* start--文本行数限制--start */
.u-line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2 {
  -webkit-line-clamp: 2;
}
.u-line-3 {
  -webkit-line-clamp: 3;
}
.u-line-4 {
  -webkit-line-clamp: 4;
}
.u-line-5 {
  -webkit-line-clamp: 5;
}
.u-line-2, .u-line-3, .u-line-4, .u-line-5 {
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
/* end--文本行数限制--end */
/* start--Retina 屏幕下的 1px 边框--start */
.u-border,
.u-border-bottom,
.u-border-left,
.u-border-right,
.u-border-top,
.u-border-top-bottom {
  position: relative;
}
.u-border-bottom:after,
.u-border-left:after,
.u-border-right:after,
.u-border-top-bottom:after,
.u-border-top:after,
.u-border:after {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  box-sizing: border-box;
  transform-origin: 0 0;
  width: 199.8%;
  height: 199.7%;
  transform: scale(0.5, 0.5);
  border: 0 solid #e4e7ed;
  z-index: 2;
}
.u-border-top:after {
  border-top-width: 1px;
}
.u-border-left:after {
  border-left-width: 1px;
}
.u-border-right:after {
  border-right-width: 1px;
}
.u-border-bottom:after {
  border-bottom-width: 1px;
}
.u-border-top-bottom:after {
  border-width: 1px 0;
}
.u-border:after {
  border-width: 1px;
}
/* end--Retina 屏幕下的 1px 边框--end */
/* start--clearfix--start */
.u-clearfix:after,
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}
/* end--clearfix--end */
/* start--高斯模糊tabbar底部处理--start */
.u-blur-effect-inset {
  width: 750rpx;
  height: var(--window-bottom);
  background-color: #FFFFFF;
}
/* end--高斯模糊tabbar底部处理--end */
/* start--提升H5端uni.toast()的层级，避免被uView的modal等遮盖--start */
/* end--提升H5端uni.toast()的层级，避免被uView的modal等遮盖--end */
/* start--去除button的所有默认样式--start */
.u-reset-button {
  padding: 0;
  font-size: inherit;
  line-height: inherit;
  background-color: transparent;
  color: inherit;
}
.u-reset-button::after {
  border: none;
}
/* end--去除button的所有默认样式--end */
/* start--微信小程序编译后页面有组件名的元素，特别处理--start */
u-td, u-th {
  flex: 1;
  align-self: stretch;
}
.u-td {
  height: 100%;
}
u-icon {
  display: inline-flex;
  align-items: center;
}
u-grid {
  width: 100%;
  flex: 0 0 100%;
}
u-line {
  flex: 1;
}
u-switch {
  display: inline-flex;
  align-items: center;
}
u-dropdown {
  flex: 1;
}
/* end-微信小程序编译后页面有组件名的元素，特别处理--end */
/* start--头条小程序编译后页面有组件名的元素，特别处理--start */
/* end-头条小程序编译后页面有组件名的元素，特别处理--end */
.diygw-form-item-small {
  line-height: 1.2 !important;
  padding: 0 12rpx;
}
.diygw-form-item-small.u-border-bottom:after {
  right: 10rpx;
  left: 10rpx;
}
.diygw-form-item-small .u-form-item {
  line-height: 1 !important;
  padding: 0 12rpx !important;
}
.u-navbar .u-form-item--right, .diygw-form-border .u-form-item--right {
  padding: 0px !important;
}
.diygw-form-item {
  padding: 20rpx 32rpx !important;
}
.diygw-uform-item .u-form-item--right {
  padding: 0 !important;
}
.diygw-form-item-top .u-form-item--left, .diygw-form-item-top .u-checkbox, .diygw-form-item-top .u-radio {
  align-items: start !important;
}
.u-form-item {
  padding: 20rpx 32rpx;
}
.u-form-item.diygw-form-item-small {
  line-height: 1.2 !important;
  padding: 6rpx 12rpx;
}
.u-form-item.diygw-form-item-small.u-border-bottom:after {
  right: 10rpx;
  left: 10rpx;
}
.u-form-item.diygw-form-item-small .u-form-item {
  line-height: 1.2 !important;
}
.u-form-item .diygw-icon {
  width: 50rpx !important;
  height: 50rpx !important;
}
.u-form-item.u-border-bottom:after {
  position: absolute;
  box-sizing: border-box;
  content: " ";
  pointer-events: none;
  right: 0.75rem;
  bottom: 0;
  left: 0.75rem;
  width: auto;
  border-bottom: 1px solid #ebedf0;
  transform: scaleY(0.5);
}
.u-form-item .diygw-tag {
  max-height: 48rpx;
  line-height: 48rpx;
}
.u-form-item .diygw-tag + .u-input {
  margin-left: 20rpx;
}
.u-form-item.solid {
  padding: 0 20rpx;
}
.u-form-item.solid:after {
  border-radius: 16rpx;
}
.u-form-item.solid.radius {
  overflow: hidden;
}
.u-form-item.solid.radius:after {
  border-radius: 50px;
}
.u-form-item .u-form-item--right__content__slot u-input, .u-form-item .u-form-item--right__content__slot diy-selectinput {
  display: flex;
  flex: 1;
  align-items: center;
}
.u-form-item .u-input.solid {
  border-radius: 12rpx;
  padding: 16rpx !important;
}
.u-form-item .u-input + .diygw-tag, .u-form-item .u-input + .diygw-tag-text {
  margin-left: 20rpx;
}
.u-form-item .u-input + .diygw-tag-text {
  border: 0;
  padding: 0;
  background: transparent;
}
.u-form-item .u-input + .diygw-tag-text:after {
  display: none;
}
.u-form-item .u-input .uni-input-input, .u-form-item .u-input .u-input__input {
  color: inherit !important;
  font-size: inherit !important;
  min-height: auto !important;
}
.u-form-item .solid {
  border-radius: 12rpx;
  padding: 16rpx !important;
}
.u-form-item .solid.radius {
  border-radius: 2000rpx;
}
.u-form-item .input.solid {
  align-items: center;
  display: flex;
  flex: 1;
  width: 100%;
  line-height: 1;
  padding: 16rpx !important;
  box-sizing: border-box;
  background-color: transparent;
}
.u-form-item .input.solid [class*=diy-icon-] {
  margin-right: 10rpx;
  max-height: 48rpx;
  margin-top: 0px;
}
.u-form-item .input.solid:after {
  border-radius: 16rpx;
}
.u-form-item .input.solid.radius:after {
  border-radius: 500rpx;
}
.u-form-item .input.solid .u-input__input {
  min-height: auto !important;
}
.u-form-item.diygw-form-item-small .solid, .u-form-item.diygw-form-item-small .input.solid {
  padding: 12rpx 16rpx !important;
}
.u-form-item.diygw-form-item-small .solid .diygw-text-lg, .u-form-item.diygw-form-item-small .input.solid .diygw-text-lg {
  font-size: 32rpx;
}
.u-form-item.diygw-form-item-small.diygw-form-item-notpadding .solid, .u-form-item.diygw-form-item-small.diygw-form-item-notpadding .input.solid {
  padding: 20rpx 16rpx !important;
}
.u-form-item.diygw-form-item-notpadding {
  padding: 0px;
}
.u-form-item.diygw-form-item-notpadding .u-form-item--right {
  padding: 20rpx 16rpx;
}
.diygw-form-item-small.u-form-item .solid, .diygw-form-item-small.u-form-item .input.solid {
  padding: 12rpx 16rpx !important;
}
.diygw-form-item-small.u-form-item .solid .diygw-text-lg, .diygw-form-item-small.u-form-item .input.solid .diygw-text-lg {
  font-size: 32rpx;
}
.diygw-form-item-small.diygw-form-item-notpadding.u-form-item .solid, .diygw-form-item-small.diygw-form-item-notpadding.u-form-item .input.solid {
  padding: 20rpx 16rpx !important;
}
.diygw-form-item-notpadding .u-form-item {
  padding: 0px;
}
.diygw-form-item-notpadding .u-form-item .u-form-item--right {
  padding: 20rpx 16rpx;
}
.flex .u-char-box, .flex .u-subsection {
  flex: 1;
}
.flex .list-cell {
  display: flex;
  box-sizing: border-box;
  width: 100%;
  padding: 10px 24rpx;
  overflow: hidden;
  color: #606266;
  font-size: 14px;
  line-height: 24px;
  background-color: #fff;
}
.diy-select .u-radio, .diy-select .u-checkbox {
  width: 100% !important;
  margin: 12rpx 0;
}
.diy-select .u-radio__label, .diy-select .u-checkbox__label {
  flex: 1 !important;
}
.diy-qrcode-page .uni-video-container {
  background: none;
}
.diy-qrcode-page .uni-video-cover {
  display: none !important;
}
.flex diy-car-input {
  flex: 1;
}
.u-scroll-list__scroll-view__content > view, .u-scroll-list__scroll-view__content > u-form-item, .u-scroll-list__scroll-view__content > u-form {
  flex-shrink: 0;
}page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}