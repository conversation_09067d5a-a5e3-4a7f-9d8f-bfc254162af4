/* components/custom-navbar/custom-navbar.wxss */

.header-navbar .diygw-tabs {
  justify-content: center;
  padding: 0 0 16rpx;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 20rpx 20rpx;
  background-color: white;
}

.header-navbar .diygw-tabs .current {
  border: none;
  color: rgba(255, 67, 145, 1);
  font-weight: bold;
  font-size: 32rpx;
  text-align: center;
  position: relative;
}
.header-navbar .diygw-tabs .arrow{
  display: none;
  width: 20rpx;
  height: 18rpx;
  position: absolute;
  top: 10rpx;
}
.header-navbar .diygw-tabs .arrow.right{
  right: 20rpx;
}
.header-navbar .diygw-tabs .tail {
  display: none;
  width: 40rpx;  
  height: 21rpx; 
  position: absolute;
  left: calc(50% - 20rpx);
  bottom: 0;
}
.header-navbar .diygw-tabs .current .tail,.header-navbar .diygw-tabs .current .arrow{
  display: block;
}