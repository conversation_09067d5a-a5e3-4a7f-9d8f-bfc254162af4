import request, { analysisRes } from "../request";
import config from "../config";

const { review } = config.apiUrls;

export default {
  /**
   * 根据订单ID获取评价详情
   * @param {string} orderId 订单ID
   */
  async getByOrderId(orderId) {
    const res = await request.get(review.getByOrderId.replace('{orderId}', orderId), {
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  /**
   * 获取评价列表
   * @param {Object} params 查询参数
   */
  async getList(params = {}) {
    const res = await request.get(review.list, {
      hideLoading: true,
      ...params,
    });
    const data = analysisRes(res);
    return data;
  },
};
