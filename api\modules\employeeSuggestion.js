import request, { analysisRes } from '../request';
import config from '../config';

const { employeeSuggestion } = config.apiUrls;

export default {
  /**
   * 获取员工建议列表
   * @param {string} employeeId 员工ID
   * @param {object} params 查询参数
   * @param {number} [params.current] 当前页码
   * @param {number} [params.pageSize] 每页数量
   * @param {string} [params.subCategory] 建议类型 (platform/service/workflow)
   * @param {string} [params.status] 状态 (pending/processing/resolved/closed)
   * @param {string} [params.keyword] 关键词搜索
   * @param {string} [params.startDate] 开始日期
   * @param {string} [params.endDate] 结束日期
   * @returns {Promise<any>} 返回建议列表
   */
  async list(employeeId, params = {}) {
    const res = await request.get(
      employeeSuggestion.list.replace('{employeeId}', employeeId),
      params
    );
    return analysisRes(res);
  },

  /**
   * 获取员工建议详情
   * @param {string} employeeId 员工ID
   * @param {string} suggestionId 建议ID
   * @returns {Promise<any>} 返回建议详情
   */
  async detail(employeeId, suggestionId) {
    const res = await request.get(
      employeeSuggestion.detail
        .replace('{employeeId}', employeeId)
        .replace('{id}', suggestionId)
    );
    return analysisRes(res);
  },

  /**
   * 创建员工建议
   * @param {string} employeeId 员工ID
   * @param {object} suggestionData 建议数据
   * @param {string} suggestionData.subCategory 建议类型 (platform/service/workflow)
   * @param {string} suggestionData.title 标题
   * @param {string} suggestionData.content 内容
   * @param {string} [suggestionData.contactInfo] 联系方式（可选）
   * @param {array} [suggestionData.photoURLs] 图片URL数组（可选，最多6张）
   * @returns {Promise<any>} 返回创建结果
   */
  async create(employeeId, suggestionData) {
    const res = await request.post(
      employeeSuggestion.create.replace('{employeeId}', employeeId), 
      suggestionData
    );
    return analysisRes(res);
  },

  /**
   * 更新员工建议
   * @param {string} employeeId 员工ID
   * @param {string} suggestionId 建议ID
   * @param {object} suggestionData 更新数据
   * @returns {Promise<any>} 返回更新结果
   */
  async update(employeeId, suggestionId, suggestionData) {
    const res = await request.put(
      employeeSuggestion.update
        .replace('{employeeId}', employeeId)
        .replace('{id}', suggestionId),
      suggestionData
    );
    return analysisRes(res);
  },

  /**
   * 删除员工建议
   * @param {string} employeeId 员工ID
   * @param {string} suggestionId 建议ID
   * @returns {Promise<any>} 返回删除结果
   */
  async delete(employeeId, suggestionId) {
    const res = await request.delete(
      employeeSuggestion.delete
        .replace('{employeeId}', employeeId)
        .replace('{id}', suggestionId)
    );
    return analysisRes(res);
  }
};
