<view class="service-photo-upload" wx:if="{{show}}">
  <view class="upload-mask" bindtap="cancelUpload"></view>
  
  <view class="upload-container">
    <!-- 标题 -->
    <view class="upload-header">
      <text class="upload-title">
        {{photoType === 'before' ? '上传服务前照片' : '上传服务后照片'}}
      </text>
      <text class="upload-subtitle">
        已上传{{photoList.length}}张，还可上传{{maxCount - photoList.length}}张照片（可选）
      </text>
    </view>

    <!-- 照片上传区域 -->
    <view class="photo-container">
      <!-- 已上传的图片 -->
      <view wx:for="{{photoList}}" wx:key="index" class="photo-item">
        <image
          src="{{item}}"
          class="photo-preview"
          mode="aspectFill"
          bindtap="previewImage"
          data-url="{{item}}"
        ></image>
        <view class="photo-delete" bindtap="deletePhoto" data-index="{{index}}">
          <text class="delete-icon">×</text>
        </view>
        <!-- 照片序号 -->
        <view class="photo-index">{{index + 1}}</view>
      </view>
      
      <!-- 上传按钮 -->
      <view 
        wx:if="{{photoList.length < maxCount && !uploading}}" 
        class="photo-upload" 
        bindtap="chooseImage"
      >
        <view class="upload-icon">
          <text class="upload-text">📷</text>
        </view>
        <text class="upload-label">添加照片</text>
      </view>

      <!-- 上传中状态 -->
      <view wx:if="{{uploading}}" class="photo-uploading">
        <view class="uploading-icon">
          <text class="uploading-text">⏳</text>
        </view>
        <text class="uploading-label">上传中...</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="upload-actions">
      <view class="action-btn cancel-btn" bindtap="cancelUpload">
        取消
      </view>
      <view class="action-btn confirm-btn" bindtap="confirmUpload">
        确定
      </view>
    </view>
  </view>
</view>
