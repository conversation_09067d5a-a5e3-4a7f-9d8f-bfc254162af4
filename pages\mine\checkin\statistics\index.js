// pages/mine/checkin/statistics/index.js
import checkinApi from '../../../../api/modules/employee-checkin.js';
import Session from '../../../../common/Session.js';

Page({
  data: {
    userInfo: {},
    statistics: {
      totalCount: 0,
      thisMonthCount: 0,
      thisWeekCount: 0,
      todayCount: 0,
      averagePerDay: 0,
      averagePerWeek: 0,
      averagePerMonth: 0,
      monthlyData: [], // 月度统计数据
      weeklyData: [], // 周度统计数据
      recentDays: [] // 最近7天数据
    },
    loading: true,
    selectedTab: 'overview', // overview, monthly, weekly, daily
  },

  onLoad() {
    this.initUserInfo();
    this.loadStatistics();
  },

  // 初始化用户信息
  initUserInfo() {
    const userInfo = Session.getUser();
    if (!userInfo || !userInfo.id) {
      wx.redirectTo({
        url: '/pages/login/index',
      });
      return;
    }
    this.setData({ userInfo });
  },

  // 加载统计数据
  async loadStatistics() {
    this.setData({ loading: true });
    
    try {
      const result = await checkinApi.getStatistics(this.data.userInfo.id);
      
      if (result) {
        // 处理统计数据
        const processedStats = this.processStatistics(result);
        
        this.setData({
          statistics: processedStats,
          loading: false
        });
      } else {
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 处理统计数据
  processStatistics(data) {
    const stats = {
      totalCount: data.totalCount || 0,
      thisMonthCount: data.thisMonthCount || 0,
      thisWeekCount: data.thisWeekCount || 0,
      todayCount: data.todayCount || 0,
      monthlyData: data.monthlyData || [],
      weeklyData: data.weeklyData || [],
      recentDays: data.recentDays || []
    };

    // 计算平均值
    const now = new Date();
    const currentMonth = now.getMonth() + 1;
    const daysInMonth = new Date(now.getFullYear(), currentMonth, 0).getDate();
    const dayOfMonth = now.getDate();
    
    // 日均打卡（本月）
    stats.averagePerDay = dayOfMonth > 0 ? (stats.thisMonthCount / dayOfMonth).toFixed(1) : 0;
    
    // 周均打卡（本月）
    const weeksInMonth = Math.ceil(dayOfMonth / 7);
    stats.averagePerWeek = weeksInMonth > 0 ? (stats.thisMonthCount / weeksInMonth).toFixed(1) : 0;
    
    // 月均打卡（基于总数和注册时间，这里简化处理）
    stats.averagePerMonth = stats.totalCount > 0 ? (stats.totalCount / Math.max(1, currentMonth)).toFixed(1) : 0;

    return stats;
  },

  // 切换标签
  switchTab(e) {
    const { tab } = e.currentTarget.dataset;
    this.setData({ selectedTab: tab });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadStatistics().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 格式化数字显示
  formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  },

  // 获取月份名称
  getMonthName(month) {
    const months = [
      '1月', '2月', '3月', '4月', '5月', '6月',
      '7月', '8月', '9月', '10月', '11月', '12月'
    ];
    return months[month - 1] || month + '月';
  },

  // 获取星期名称
  getWeekName(week) {
    const weeks = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    return weeks[week - 1] || '第' + week + '周';
  },

  // 查看详细记录
  viewDetails(e) {
    const { type, date } = e.currentTarget.dataset;
    
    let url = '/pages/mine/checkin/history/index';
    
    if (type === 'today') {
      // 查看今日记录
      const today = new Date().toISOString().split('T')[0];
      url += `?startDate=${today}&endDate=${today}`;
    } else if (type === 'month') {
      // 查看本月记录
      const now = new Date();
      const startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
      const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];
      url += `?startDate=${startDate}&endDate=${endDate}`;
    } else if (type === 'week') {
      // 查看本周记录
      const now = new Date();
      const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay() + 1));
      const endOfWeek = new Date(now.setDate(now.getDate() - now.getDay() + 7));
      const startDate = startOfWeek.toISOString().split('T')[0];
      const endDate = endOfWeek.toISOString().split('T')[0];
      url += `?startDate=${startDate}&endDate=${endDate}`;
    } else if (date) {
      // 查看指定日期记录
      url += `?startDate=${date}&endDate=${date}`;
    }
    
    wx.navigateTo({ url });
  }
});
