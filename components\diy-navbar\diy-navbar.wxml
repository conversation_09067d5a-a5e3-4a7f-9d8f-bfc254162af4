<view>
  <view class="diy-navbar  diygw-top" style="{{'height:'+(CustomBar+'px')+';'}}">
    <view class="diygw-title flex fixed {{bgColor}} {{bgImage!=''?'none-bg text-white bg-img':''}}" style="padding-right:0;height:{{CustomBar}}px;padding-top:{{StatusBar}}px;padding-left:{{isCapsule?10:0}}px;padding-right:{{isCapsule?10:0}}px;{{bgCustom?'background-color:'+bgCustom+' !important;':''}}{{color?'color:'+color+' !important;':''}}{{bgImage?'background-image:url('+bgImage+')':''}}">
      <view class="diy-navbar-tool"  style="{{'top:'+(StatusBar+'px')+';'}}"  wx:if="{{isBack||isHome}}" v-if="isBack||isHome">
					<view class="diy-navbar-tool-inner {{isCapsule?'diy-navbar-tool-solid':''}}" style="border:{{isCapsule?'1px solid '+color:'none'}}">
            <block wx:if="{{isBack}}">
              <view  class="action flex align-center" bindtap="BackPage"><text class="diy-icon-back title-bar-icon"></text>
                <slot name="backText"></slot>
              </view>
            </block>
          
            <block wx:if="{{isHome}}">
              <view class="diy-navbar-split"></view>
              <view class="title-bar-icon diy-icon-home titlebar-icon" bindtap="BackHome"></view>
            </block>
					</view>
				</view>
     
      <view class="content title flex1 text-center" style="{{'top:'+(StatusBar+'px')+';'}}">
        <slot name="content"></slot>
      </view>
      <slot name="right"></slot>
    </view>
  </view>
  <view  style="{{'padding-top:'+(CustomBar+'px')+';'}}"></view>
</view>