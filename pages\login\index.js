import userApi from "../../api/modules/user";

// login.js
Page({
  data: {
    //用户全局信息
    userInfo: {
      avatarUrl: "",
      nickName: "",
    },
    globalData: {
      agree: "0",
    },
    showEditPopup: false,
    phone: "",
  },

  // 未同意协议时点击按钮的处理方法
  handleAgreeFirst() {
    // 添加震动反馈
    wx.vibrateShort({
      type: "medium",
    });
    wx.showToast({
      title: "请先阅读并同意用户协议和隐私协议",
      icon: "none",
      duration: 2000,
    });
  },

  async getPhoneNumber(e) {
    if (e.detail.errMsg === "getPhoneNumber:ok") {
      const { encryptedData, iv, code } = e.detail;
      try {
        const res = await userApi.getPhoneNumber(code);
        if (res && res.phone_info && res.phone_info.phoneNumber) {
          this.login(res.phone_info.phoneNumber);
        } else {
          console.error("接口未返回手机号");
          console.log(res);
          wx.showModal({
            title: "提示",
            content: "手机号未找到，请重试",
          });
        }
      } catch (error) {
        console.error("获取手机号失败:", error);
        wx.showModal({
          title: "提示",
          content: "获取手机号失败，请重试",
        });
      }
    } else {
      wx.showToast({
        title: "需要授权手机号才能继续使用",
        icon: "none",
      });
    }
  },

  async login(phoneNumber) {
    const thiz = this;
    const res = await userApi.login(phoneNumber);
    // console.log(res);
    if (res) {
      const { code, user, token } = res || {};
      if (code === 404) { // 约定返回404状态码表示未注册
        thiz.navigateTo({
          type: "tip",
          tip: "该手机号未注册，请联系管理员",
        });
        return;
      }
      wx.setStorageSync("userInfo", res.userInfo);
      const userInfo = {
        ...user,
        token,
      };
      thiz.setData({ userInfo });
      thiz.$session.setUser(userInfo);

      // 登录成功后启动位置服务
      const app = getApp();
      if (app && app.onUserLogin) {
        app.onUserLogin();
      }

      this.redirect();
    } else {
      wx.showToast({
        title: "登录失败，请重试",
        icon: "none",
      });
    }
  },

  // agree 自定义方法
  async agreeFunction(param) {
    let that = this.data;
    //如果不同意，改为同意
    this.setData({
      globalData: {
        agree: that.globalData.agree == "1" ? "0" : "1",
      },
    });
  },

  redirect() {
    wx.switchTab({
      url: "/pages/index/index",
    });
  },
  async handleRegister() {
    const { userInfo, phone } = this.data;

    if (!userInfo.nickName || !phone) {
      wx.showToast({
        title: "请填写完整信息",
        icon: "none",
      });
      return;
    }

    try {
      // 这里调用你的注册接口
      await wx.request({
        url: "YOUR_API_URL/register",
        method: "POST",
        data: {
          avatarUrl: userInfo.avatarUrl,
          nickName: userInfo.nickName,
          phone: phone,
        },
      });

      wx.showToast({
        title: "注册成功",
        icon: "success",
      });

      this.setData({
        showEditPopup: false,
      });

      // 注册成功后的跳转逻辑
      wx.switchTab({
        url: "/pages/index/index",
      });
    } catch (error) {
      wx.showToast({
        title: "注册失败",
        icon: "none",
      });
    }
  },
  onClosePopup() {
    this.setData({
      showEditPopup: false,
    });
  },
});
