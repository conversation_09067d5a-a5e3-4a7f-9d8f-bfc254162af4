import request, { analysisRes } from "../request";
import config from "../config";

const { employeeCheckin } = config.apiUrls;

export default {
  // 创建打卡记录
  async create(data) {
    console.log('API调用 - 创建打卡记录:', employeeCheckin.create, data);
    const res = await request.post(employeeCheckin.create, data);
    console.log('API响应 - 创建打卡记录:', res);
    const result = analysisRes(res);
    return result;
  },

  // 获取员工打卡记录列表
  async getList(employeeId, params = {}) {
    const url = employeeCheckin.list.replace('{employeeId}', employeeId);
    const res = await request.get(url, {
      ...params,
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  // 获取今日打卡记录
  async getTodayList(employeeId) {
    const url = employeeCheckin.todayList.replace('{employeeId}', employeeId);
    const res = await request.get(url, {
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  // 获取打卡统计信息
  async getStatistics(employeeId, params = {}) {
    const url = employeeCheckin.statistics.replace('{employeeId}', employeeId);
    const res = await request.get(url, {
      ...params,
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  // 删除打卡记录
  async delete(id) {
    const url = employeeCheckin.delete.replace('{id}', id);
    const res = await request.post(url, {});
    const result = analysisRes(res);
    return result;
  },
};
