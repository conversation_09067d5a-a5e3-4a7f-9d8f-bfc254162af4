import request, { analysisRes } from "../request";
import config from "../config";

const { employeeCheckin } = config.apiUrls;

export default {
  // 创建打卡记录
  async create(data) {
    console.log('API调用 - 创建打卡记录:', employeeCheckin.create, data);
    const res = await request.post(employeeCheckin.create, data);
    console.log('API响应 - 创建打卡记录:', res);
    const result = analysisRes(res);
    return result;
  },

  // 获取员工打卡记录列表
  async getList(employeeId, params = {}) {
    const url = employeeCheckin.list.replace('{employeeId}', employeeId);
    const res = await request.get(url, {
      ...params,
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  // 获取今日打卡记录
  async getTodayList(employeeId) {
    const url = employeeCheckin.todayList.replace('{employeeId}', employeeId);
    const res = await request.get(url, {
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  // 获取打卡统计信息
  async getStatistics(employeeId, params = {}) {
    const url = employeeCheckin.statistics.replace('{employeeId}', employeeId);
    const res = await request.get(url, {
      ...params,
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  // 删除打卡记录
  async delete(id) {
    const url = employeeCheckin.delete.replace('{id}', id);
    const res = await request.post(url, {});
    const result = analysisRes(res);
    return result;
  },

  // 检查员工最后打卡时间
  async getLastCheckinTime(employeeId) {
    try {
      // 优先使用专门的API接口
      const url = employeeCheckin.lastCheckinTime.replace('{employeeId}', employeeId);
      const res = await request.get(url, {
        hideLoading: true,
      });
      const data = analysisRes(res);
      return data ? data.lastCheckinTime : null;
    } catch (error) {
      console.log('专门API不可用，使用列表API获取最后打卡时间');
      // 如果专门的API不可用，回退到使用列表API
      const res = await this.getList(employeeId, {
        current: 1,
        pageSize: 1,
        hideLoading: true,
      });

      if (res && res.list && res.list.length > 0) {
        return res.list[0].checkInTime;
      }
      return null;
    }
  },

  // 检查是否需要强制打卡（超过一周未打卡）
  async checkNeedForceCheckin(employeeId) {
    try {
      const lastCheckinTime = await this.getLastCheckinTime(employeeId);

      if (!lastCheckinTime) {
        // 从未打卡，需要强制打卡
        return true;
      }

      const lastCheckin = new Date(lastCheckinTime);
      const now = new Date();
      const diffTime = now.getTime() - lastCheckin.getTime();
      const diffDays = diffTime / (1000 * 60 * 60 * 24);

      // 超过7天未打卡，需要强制打卡
      return diffDays > 7;
    } catch (error) {
      console.error('检查打卡状态失败:', error);
      // 出错时不强制打卡，避免影响正常使用
      return false;
    }
  },
};
