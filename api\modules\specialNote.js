import request, { analysisRes } from "../request";
import config from "../config";

const { specialNote } = config.apiUrls;

export default {
  /**
   * 添加订单特殊情况说明
   * @param {number} orderId 订单ID
   * @param {number} employeeId 员工ID
   * @param {string} content 特殊情况说明内容
   * @param {string[]} photos 特殊情况图片URL数组
   */
  async create(orderId, employeeId, content, photos = []) {
    const res = await request.post(specialNote.create, {
      orderId,
      employeeId,
      content,
      photos,
    });
    const data = analysisRes(res);
    return data;
  },

  /**
   * 更新订单特殊情况说明
   * @param {number} orderId 订单ID
   * @param {number} employeeId 员工ID
   * @param {string} content 特殊情况说明内容
   * @param {string[]} photos 特殊情况图片URL数组
   */
  async update(orderId, employeeId, content, photos = []) {
    const res = await request.put(specialNote.update.replace('{orderId}', orderId), {
      employeeId,
      content,
      photos,
    });
    const data = analysisRes(res);
    return data;
  },

  /**
   * 查询订单特殊情况说明
   * @param {number} orderId 订单ID
   */
  async get(orderId) {
    const res = await request.get(specialNote.get.replace('{orderId}', orderId), {
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },

  /**
   * 删除订单特殊情况说明
   * @param {number} orderId 订单ID
   * @param {number} employeeId 员工ID
   */
  async delete(orderId, employeeId) {
    const res = await request.delete(specialNote.delete.replace('{orderId}', orderId), {
      employeeId,
    });
    const data = analysisRes(res);
    return data;
  },
};
