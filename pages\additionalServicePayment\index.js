import { formatNormalDate } from '../utils/util';
import orderApi from '../../api/modules/order';
import Session from '../../common/Session';

Page({
  data: {
    orderDetailId: '', // 订单详情ID
    additionalServiceId: '', // 追加服务ID
    additionalService: {}, // 追加服务信息
    orderInfo: {}, // 订单信息
    userInfo: null, // 用户信息
    loading: false, // 支付加载状态
    debugMode: true, // 调试模式，跳过实际微信支付
  },

  onLoad(options) {
    console.log('=== 追加服务支付页面启动 ===');
    console.log('接收参数:', options);

    // 获取用户信息
    const userInfo = Session.getUser();
    console.log('当前用户信息:', userInfo);

    if (!userInfo) {
      console.error('用户未登录，返回上一页');
      wx.showToast({
        title: '请先登录',
        icon: 'none',
      });
      wx.navigateBack();
      return;
    }

    // 验证必要参数
    if (!options.orderDetailId || !options.additionalServiceId) {
      console.error('缺少必要参数:', { orderDetailId: options.orderDetailId, additionalServiceId: options.additionalServiceId });
      wx.showToast({
        title: '参数错误',
        icon: 'none',
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      userInfo,
      orderDetailId: options.orderDetailId,
      additionalServiceId: options.additionalServiceId,
    });

    console.log('页面数据初始化完成，开始加载服务信息');
    // 加载追加服务信息
    this.loadAdditionalServiceInfo();
  },

  // 加载追加服务信息
  async loadAdditionalServiceInfo() {
    const { orderDetailId, additionalServiceId } = this.data;
    
    if (!orderDetailId || !additionalServiceId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none',
      });
      wx.navigateBack();
      return;
    }

    wx.showLoading({
      title: '加载中...',
    });

    try {
      console.log('加载追加服务信息 - 开始', { orderDetailId, additionalServiceId });
      
      // 获取所有追加服务列表
      const allServices = await orderApi.getAdditionalServices(orderDetailId);
      console.log('获取追加服务列表结果', allServices);
      
      if (!allServices || !Array.isArray(allServices)) {
        throw new Error('获取追加服务列表失败');
      }

      // 找到当前要支付的追加服务
      const currentService = allServices.find(service => service.id === additionalServiceId);
      console.log('当前追加服务信息', currentService);
      
      if (!currentService) {
        throw new Error('未找到指定的追加服务');
      }

      // 检查服务状态是否可以支付
      if (currentService.status !== 'confirmed' && currentService.status !== 'pending_payment') {
        wx.showToast({
          title: '该服务不可支付',
          icon: 'none',
        });
        wx.navigateBack();
        return;
      }

      // 格式化服务信息
      const formattedService = {
        ...currentService,
        createdAt: currentService.createdAt ? formatNormalDate(currentService.createdAt) : null,
        confirmTime: currentService.confirmTime ? formatNormalDate(currentService.confirmTime) : null,
        statusText: this.getAdditionalServiceStatusText(currentService.status),
        serviceName: currentService.details?.[0]?.serviceName || currentService.serviceName || '追加服务',
        servicePrice: currentService.details?.[0]?.servicePrice || currentService.totalFee || 0,
        originalPrice: currentService.originalPrice || 0,
      };

      // 设置订单信息（从追加服务中获取）
      const orderInfo = {
        id: currentService.orderDetail?.order?.id || '',
        sn: currentService.orderDetail?.order?.sn || '',
        customerName: currentService.customer?.name || currentService.customer?.nickname || '未知客户',
        customerPhone: currentService.customer?.phone || '',
      };

      this.setData({
        additionalService: formattedService,
        orderInfo: orderInfo,
      });

      console.log('追加服务信息加载完成', { formattedService, orderInfo });

    } catch (error) {
      console.error('加载追加服务信息失败', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none',
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      wx.hideLoading();
    }
  },

  // 获取追加服务状态文字
  getAdditionalServiceStatusText(status) {
    const statusMap = {
      pending_confirm: '待确认',
      confirmed: '已确认',
      rejected: '已拒绝',
      pending_payment: '待付款',
      paid: '已付款',
      completed: '已完成',
      cancelled: '已取消',
      refunding: '退款中',
      refunded: '已退款',
    };
    return statusMap[status] || status;
  },

  // 发起支付
  async handlePayment() {
    if (this.data.loading) {
      return;
    }

    const { orderDetailId, additionalServiceId, additionalService } = this.data;
    
    console.log('发起支付 - 开始', { orderDetailId, additionalServiceId, additionalService });

    this.setData({ loading: true });

    wx.showLoading({
      title: '正在支付...',
    });

    try {
      // 1. 获取支付参数
      console.log('=== 步骤1: 获取支付参数 ===');
      let payParams;

      try {
        payParams = await orderApi.getAdditionalServicePayParams(orderDetailId, additionalServiceId);
      } catch (apiError) {
        console.warn('API获取支付参数失败，使用模拟数据:', apiError);
        // 如果后台接口还没准备好，使用模拟数据进行测试
        payParams = this.generateMockPayParams();
      }

      if (!payParams) {
        throw new Error('获取支付参数失败');
      }

      console.log('获取到支付参数:', payParams);

      // 2. 调用微信支付
      console.log('=== 步骤2: 调用微信支付 ===');
      if (this.data.debugMode) {
        console.log('调试模式：跳过实际微信支付，模拟支付成功');
        // 模拟支付延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        await this.requestWechatPayment(payParams);
      }

      // 3. 支付成功后的处理
      console.log('=== 步骤3: 支付成功处理 ===');
      await this.handlePaymentSuccess();

    } catch (error) {
      console.error('=== 支付流程失败 ===', error);
      this.handlePaymentError(error);
    } finally {
      this.setData({ loading: false });
      wx.hideLoading();
    }
  },

  // 调用微信支付
  async requestWechatPayment(payParams) {
    return new Promise((resolve, reject) => {
      console.log('=== 开始调用微信支付 ===');
      console.log('支付参数详情:', payParams);

      // 验证支付参数
      const requiredFields = ['timeStamp', 'nonceStr', 'package', 'paySign'];
      const missingFields = requiredFields.filter(field => !payParams[field]);

      if (missingFields.length > 0) {
        console.error('支付参数缺失:', missingFields);
        reject(new Error(`支付参数缺失: ${missingFields.join(', ')}`));
        return;
      }

      const paymentParams = {
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        package: payParams.package,
        signType: payParams.signType || 'MD5',
        paySign: payParams.paySign,
      };

      console.log('最终支付参数:', paymentParams);

      wx.requestPayment({
        ...paymentParams,
        success: (res) => {
          console.log('=== 微信支付成功 ===');
          console.log('支付成功回调:', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('=== 微信支付失败 ===');
          console.error('支付失败回调:', err);
          console.error('错误详情:', {
            errMsg: err.errMsg,
            errCode: err.errCode,
          });
          reject(err);
        }
      });
    });
  },

  // 支付成功处理
  async handlePaymentSuccess() {
    const { orderDetailId, additionalServiceId } = this.data;

    console.log('=== 开始处理支付成功回调 ===');
    console.log('订单详情ID:', orderDetailId);
    console.log('追加服务ID:', additionalServiceId);

    try {
      // 确认支付成功
      console.log('调用支付确认API...');
      const confirmResult = await orderApi.confirmAdditionalServicePayment(
        orderDetailId,
        additionalServiceId,
        {
          status: 'success',
          payTime: new Date().toISOString(),
          employeeId: this.data.userInfo.id,
        }
      );

      console.log('支付确认API返回结果:', confirmResult);

      wx.showToast({
        title: '支付成功',
        icon: 'success',
        duration: 2000,
      });

      console.log('支付成功，2秒后返回上一页');
      // 延迟返回上一页
      setTimeout(() => {
        console.log('返回上一页');
        wx.navigateBack();
      }, 2000);

    } catch (error) {
      console.error('=== 支付确认失败 ===', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
      });

      // 即使确认失败，支付可能已经成功，给用户友好提示
      wx.showModal({
        title: '支付状态确认',
        content: '支付可能已成功，但状态确认失败。请返回查看订单状态，如有问题请联系客服。',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  // 支付失败处理
  handlePaymentError(error) {
    console.log('=== 处理支付错误 ===');
    console.error('错误对象:', error);

    let errorMessage = '支付失败';
    let showModal = false;

    if (error.errMsg) {
      console.log('微信支付错误信息:', error.errMsg);
      if (error.errMsg.includes('cancel')) {
        errorMessage = '支付已取消';
      } else if (error.errMsg.includes('fail')) {
        errorMessage = '支付失败，请重试';
        showModal = true;
      } else if (error.errMsg.includes('timeout')) {
        errorMessage = '支付超时，请重试';
        showModal = true;
      }
    } else if (error.message) {
      errorMessage = error.message;
      if (error.message.includes('参数')) {
        showModal = true;
      }
    }

    console.log('最终错误提示:', errorMessage);

    if (showModal) {
      wx.showModal({
        title: '支付失败',
        content: errorMessage + '\n\n如果问题持续存在，请联系客服。',
        showCancel: true,
        cancelText: '返回',
        confirmText: '重试',
        success: (res) => {
          if (res.confirm) {
            // 重新尝试支付
            this.handlePayment();
          } else {
            // 返回上一页
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000,
      });
    }
  },

  // 生成模拟支付参数（用于测试）
  generateMockPayParams() {
    console.log('=== 生成模拟支付参数 ===');
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const nonceStr = Math.random().toString(36).substr(2, 15);

    const mockParams = {
      timeStamp: timestamp,
      nonceStr: nonceStr,
      package: 'prepay_id=wx' + timestamp + nonceStr,
      signType: 'MD5',
      paySign: 'mock_pay_sign_' + nonceStr,
    };

    console.log('模拟支付参数:', mockParams);
    return mockParams;
  },

  // 查看详情
  viewDetails() {
    wx.showModal({
      title: '服务详情',
      content: `服务名称：${this.data.additionalService.serviceName}\n客户：${this.data.orderInfo.customerName}\n订单号：${this.data.orderInfo.sn}`,
      showCancel: false,
    });
  },
});
