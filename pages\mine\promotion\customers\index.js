// pages/mine/promotion/customers/index.js
import promotionApi from '../../../../api/modules/promotion';
import Session from '../../../../common/Session';

Page({
  data: {
    userInfo: null,
    customerList: [],
    loading: false,
    hasMore: true,
    current: 1,
    pageSize: 10,
    total: 0
  },

  onLoad(options) {
    this.setData({
      userInfo: Session.getUser()
    });
    this.loadCustomers();
  },

  /**
   * 加载推广客户列表
   */
  async loadCustomers(refresh = false) {
    const userInfo = this.data.userInfo;
    if (!userInfo || !userInfo.id) {
      return;
    }

    if (refresh) {
      this.setData({
        current: 1,
        customerList: [],
        hasMore: true
      });
    }

    if (this.data.loading || !this.data.hasMore) {
      return;
    }

    try {
      this.setData({ loading: true });
      
      const params = {
        current: this.data.current,
        pageSize: this.data.pageSize
      };

      const result = await promotionApi.getCustomers(userInfo.id, params);
      
      if (result && result.list) {
        const newList = refresh ? result.list : [...this.data.customerList, ...result.list];
        const hasMore = result.list.length === this.data.pageSize && newList.length < result.total;
        
        this.setData({
          customerList: newList,
          total: result.total || 0,
          current: this.data.current + 1,
          hasMore
        });
      }
    } catch (error) {
      console.error('加载推广客户列表失败:', error);
      this.showToast('加载客户列表失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
      return Math.floor(diff / 3600000) + '小时前';
    } else if (diff < 604800000) { // 1周内
      return Math.floor(diff / 86400000) + '天前';
    } else {
      return date.toLocaleDateString();
    }
  },

  /**
   * 获取会员状态文本
   */
  getMemberStatusText(status) {
    const statusMap = {
      0: '普通用户',
      1: '会员用户',
      2: 'VIP用户'
    };
    return statusMap[status] || '未知状态';
  },

  /**
   * 获取会员状态颜色
   */
  getMemberStatusColor(status) {
    const colorMap = {
      0: '#999',
      1: '#2f83ff',
      2: '#ff6b35'
    };
    return colorMap[status] || '#999';
  },

  /**
   * 显示提示信息
   */
  showToast(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadCustomers(true);
    wx.stopPullDownRefresh();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    this.loadCustomers();
  }
});
