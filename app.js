import './page-extend';
import LocationManager from './utils/LocationManager';
import Session from './common/Session';

App({
	globalData: {
		userInfo: null,
		tabBar: [],
		homePage: '/pages/index',
		pages: ['/pages/index', '/pages/index/service/second'],
		userData: {}
	},
	onLaunch() {
		wx.getSystemInfo({
			success: (e) => {
				this.globalData.StatusBar = e.statusBarHeight;
				let capsule = wx.getMenuButtonBoundingClientRect();
				this.globalData.WindowWidth = e.windowWidth;
				this.globalData.PixelRatio = 750 / e.windowWidth;
				if (capsule) {
					this.globalData.Custom = capsule;
					this.globalData.CustomBar = capsule.bottom + capsule.top - e.statusBarHeight;
				} else {
					this.globalData.CustomBar = e.statusBarHeight + 50;
				}
			}
		});

		// 检查用户登录状态，如果已登录则启动位置服务
		this.checkAndStartLocationService();
	},
	onShow() {
		// 小程序从后台进入前台时，检查位置服务状态
		this.checkAndStartLocationService();
	},
	onHide() {
		// 小程序进入后台时，保持位置服务运行
		console.log('App: 小程序进入后台，位置服务继续运行');
	},

	/**
	 * 检查并启动位置服务
	 */
	checkAndStartLocationService() {
		const userInfo = Session.getUser();
		if (userInfo && userInfo.id) {
			// 用户已登录，启动位置服务
			if (!LocationManager.isLocationServiceRunning()) {
				console.log('App: 用户已登录，启动位置服务');
				LocationManager.start();
			}
		} else {
			// 用户未登录，停止位置服务
			if (LocationManager.isLocationServiceRunning()) {
				console.log('App: 用户未登录，停止位置服务');
				LocationManager.stop();
			}
		}
	},

	/**
	 * 用户登录后调用此方法启动位置服务
	 */
	onUserLogin() {
		console.log('App: 用户登录，启动位置服务');
		LocationManager.start();
	},

	/**
	 * 用户登出后调用此方法停止位置服务
	 */
	onUserLogout() {
		console.log('App: 用户登出，停止位置服务');
		LocationManager.stop();
	}
});
