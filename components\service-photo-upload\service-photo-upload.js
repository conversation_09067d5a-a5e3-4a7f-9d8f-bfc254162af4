Component({
  properties: {
    // 照片类型：before（服务前）或 after（服务后）
    photoType: {
      type: String,
      value: 'before'
    },
    // 最大上传数量
    maxCount: {
      type: Number,
      value: 9
    },
    // 已上传的照片列表
    photoList: {
      type: Array,
      value: []
    },
    // 是否显示组件
    show: {
      type: Boolean,
      value: false
    },
    // 订单信息
    orderInfo: {
      type: Object,
      value: {}
    }
  },

  data: {
    uploading: false,
    uploadProgress: 0
  },

  methods: {
    /**
     * 选择图片
     */
    chooseImage() {
      const { photoList, maxCount } = this.data;
      const remainCount = maxCount - photoList.length;
      
      if (remainCount <= 0) {
        wx.showToast({
          title: `最多只能上传${maxCount}张照片`,
          icon: 'none'
        });
        return;
      }

      // 使用页面扩展的上传功能
      // getCurrentPages() 是小程序全局函数，IDE可能误报错误
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      if (!currentPage || !currentPage.uploadImage) {
        wx.showToast({
          title: '上传功能不可用',
          icon: 'none'
        });
        return;
      }

      this.setData({ uploading: true });

      const { orderInfo, photoType } = this.data;
      const keyPrefix = `service-photos/${orderInfo.id}/${photoType}/`;

      currentPage.uploadImage(
        currentPage,
        '', // 存储字段
        keyPrefix, // 上传key前缀
        remainCount // 最大数量
      ).then(res => {
        // 将新上传的照片与已有照片合并
        const newPhotoList = [...photoList, ...res];

        // 检查是否超过最大数量限制
        const finalPhotoList = newPhotoList.slice(0, this.data.maxCount);

        this.setData({
          photoList: finalPhotoList,
          uploading: false
        });

        // 触发照片列表更新事件
        this.triggerEvent('photoChange', {
          photoList: finalPhotoList,
          photoType: this.data.photoType
        });

        wx.showToast({
          title: `上传成功，共${finalPhotoList.length}张照片`,
          icon: 'success'
        });
      }).catch(error => {
        console.error('上传失败:', error);
        this.setData({ uploading: false });
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      });
    },

    /**
     * 删除图片
     */
    deletePhoto(e) {
      const index = e.currentTarget.dataset.index;
      const photoList = [...this.data.photoList];
      photoList.splice(index, 1);
      
      this.setData({ photoList });
      
      // 触发照片列表更新事件
      this.triggerEvent('photoChange', {
        photoList,
        photoType: this.data.photoType
      });
    },

    /**
     * 预览图片
     */
    previewImage(e) {
      const url = e.currentTarget.dataset.url;
      wx.previewImage({
        current: url,
        urls: this.data.photoList
      });
    },

    /**
     * 确认上传
     */
    confirmUpload() {
      const { photoList, photoType } = this.data;
      
      this.triggerEvent('confirm', {
        photoList,
        photoType
      });
    },

    /**
     * 取消上传
     */
    cancelUpload() {
      this.triggerEvent('cancel');
    }
  }
});
